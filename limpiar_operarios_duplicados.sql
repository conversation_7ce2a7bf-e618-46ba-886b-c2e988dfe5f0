-- Script para limpiar operarios duplicados y reorganizar la tabla
-- Ejecutar DESPUÉS de haber ejecutado el script principal mejorado

USE ApqLitalsa;

-- =====================================================
-- PASO 1: CREAR FUNCIONES DE LIMPIEZA (si no existen)
-- =====================================================

-- Función para limpiar texto (eliminar saltos de línea, espacios múltiples, etc.)
IF OBJECT_ID('dbo.LimpiarTexto', 'FN') IS NULL
BEGIN
    EXEC('
    CREATE FUNCTION dbo.LimpiarTexto(@texto NVARCHAR(255))
    RETURNS NVARCHAR(255)
    AS
    BEGIN
        DECLARE @resultado NVARCHAR(255);
        
        IF @texto IS NULL OR LTRIM(RTRIM(@texto)) = ''''
            RETURN NULL;
        
        SET @resultado = @texto;
        
        -- Eliminar saltos de línea, tabulaciones y caracteres de control
        SET @resultado = REPLACE(@resultado, CHAR(13), ''''); -- Carriage Return
        SET @resultado = REPLACE(@resultado, CHAR(10), ''''); -- Line Feed
        SET @resultado = REPLACE(@resultado, CHAR(9), '''');  -- Tab
        SET @resultado = REPLACE(@resultado, CHAR(160), '' ''); -- Non-breaking space
        
        -- Eliminar espacios múltiples y trim
        WHILE CHARINDEX(''  '', @resultado) > 0
            SET @resultado = REPLACE(@resultado, ''  '', '' '');
        
        SET @resultado = LTRIM(RTRIM(@resultado));
        
        IF @resultado = ''''
            RETURN NULL;
        
        RETURN @resultado;
    END;
    ');
END;

-- =====================================================
-- PASO 2: ANALIZAR DATOS ACTUALES
-- =====================================================

-- Ver operarios problemáticos
SELECT 
    Id,
    '[' + Operario + ']' as OperarioConCorchetes,
    LEN(Operario) as Longitud,
    Activo,
    CASE 
        WHEN Operario LIKE '%' + CHAR(13) + '%' OR Operario LIKE '%' + CHAR(10) + '%' THEN 'Tiene saltos de línea'
        WHEN LEN(Operario) - LEN(LTRIM(RTRIM(Operario))) > 0 THEN 'Tiene espacios al inicio/final'
        WHEN CHARINDEX('  ', Operario) > 0 THEN 'Tiene espacios múltiples'
        WHEN Operario = '' THEN 'Vacío'
        ELSE 'OK'
    END as Problema
FROM TablaOperarios
WHERE Operario LIKE '%' + CHAR(13) + '%' 
   OR Operario LIKE '%' + CHAR(10) + '%'
   OR LEN(Operario) - LEN(LTRIM(RTRIM(Operario))) > 0
   OR CHARINDEX('  ', Operario) > 0
   OR Operario = ''
ORDER BY Id;

-- =====================================================
-- PASO 3: CREAR TABLA TEMPORAL CON OPERARIOS LIMPIOS
-- =====================================================

-- Crear tabla temporal para operarios únicos y limpios
CREATE TABLE #OperariosLimpios (
    OperarioLimpio NVARCHAR(100),
    IdMinimo INT,
    EsOficial BIT,
    CantidadDuplicados INT
);

-- Insertar operarios únicos después de limpiar
INSERT INTO #OperariosLimpios (OperarioLimpio, IdMinimo, EsOficial, CantidadDuplicados)
SELECT 
    dbo.LimpiarTexto(Operario) as OperarioLimpio,
    MIN(Id) as IdMinimo,
    MAX(CAST(Activo AS INT)) as EsOficial, -- Si alguno está activo, mantenerlo activo
    COUNT(*) as CantidadDuplicados
FROM TablaOperarios
WHERE dbo.LimpiarTexto(Operario) IS NOT NULL
GROUP BY dbo.LimpiarTexto(Operario)
HAVING dbo.LimpiarTexto(Operario) IS NOT NULL;

-- Mostrar resumen de limpieza
SELECT 
    'Total operarios originales' as Descripcion,
    COUNT(*) as Cantidad
FROM TablaOperarios
UNION ALL
SELECT 
    'Operarios únicos después de limpiar' as Descripcion,
    COUNT(*) as Cantidad
FROM #OperariosLimpios
UNION ALL
SELECT 
    'Operarios duplicados a eliminar' as Descripcion,
    (SELECT COUNT(*) FROM TablaOperarios) - (SELECT COUNT(*) FROM #OperariosLimpios) as Cantidad;

-- =====================================================
-- PASO 4: ACTUALIZAR REFERENCIAS EN OTRAS TABLAS
-- =====================================================

-- Crear tabla de mapeo de IDs antiguos a nuevos
CREATE TABLE #MapeoOperarios (
    IdAntiguo INT,
    IdNuevo INT
);

-- Llenar tabla de mapeo
INSERT INTO #MapeoOperarios (IdAntiguo, IdNuevo)
SELECT 
    o.Id as IdAntiguo,
    ol.IdMinimo as IdNuevo
FROM TablaOperarios o
INNER JOIN #OperariosLimpios ol ON dbo.LimpiarTexto(o.Operario) = ol.OperarioLimpio;

-- Actualizar referencias en LOTESNODRIZAS
UPDATE ln
SET OperarioId = m.IdNuevo
FROM LOTESNODRIZAS ln
INNER JOIN #MapeoOperarios m ON ln.OperarioId = m.IdAntiguo
WHERE m.IdAntiguo != m.IdNuevo;

-- Actualizar referencias en Incidencias
UPDATE inc
SET CreadoPorId = m.IdNuevo
FROM Incidencias inc
INNER JOIN #MapeoOperarios m ON inc.CreadoPorId = m.IdAntiguo
WHERE m.IdAntiguo != m.IdNuevo;

-- Actualizar referencias en TablaInspecciones
UPDATE ti
SET RealizadoPorId = m.IdNuevo
FROM TablaInspecciones ti
INNER JOIN #MapeoOperarios m ON ti.RealizadoPorId = m.IdAntiguo
WHERE m.IdAntiguo != m.IdNuevo;

-- Actualizar referencias en TablaPesosEnvases
UPDATE tpe
SET RealizadoPorId = m.IdNuevo
FROM TablaPesosEnvases tpe
INNER JOIN #MapeoOperarios m ON tpe.RealizadoPorId = m.IdAntiguo
WHERE m.IdAntiguo != m.IdNuevo;

-- =====================================================
-- PASO 5: ELIMINAR OPERARIOS DUPLICADOS
-- =====================================================

-- Eliminar operarios duplicados (mantener solo el de ID mínimo)
DELETE FROM TablaOperarios
WHERE Id NOT IN (SELECT IdMinimo FROM #OperariosLimpios);

-- =====================================================
-- PASO 6: ACTUALIZAR OPERARIOS RESTANTES
-- =====================================================

-- Actualizar nombres limpios y estado activo
UPDATE o
SET 
    Operario = ol.OperarioLimpio,
    Activo = ol.EsOficial,
    FechaModificacion = GETDATE()
FROM TablaOperarios o
INNER JOIN #OperariosLimpios ol ON o.Id = ol.IdMinimo;

-- =====================================================
-- PASO 7: LIMPIAR TABLAS TEMPORALES
-- =====================================================

DROP TABLE #OperariosLimpios;
DROP TABLE #MapeoOperarios;

-- =====================================================
-- PASO 8: VERIFICAR RESULTADOS
-- =====================================================

-- Mostrar operarios finales
SELECT 
    Id,
    '[' + Operario + ']' as OperarioLimpio,
    Activo,
    FechaCreacion,
    FechaModificacion
FROM TablaOperarios
ORDER BY Operario;

-- Verificar que no hay referencias huérfanas
SELECT 'LOTESNODRIZAS' as Tabla, COUNT(*) as RegistrosHuerfanos
FROM LOTESNODRIZAS ln
LEFT JOIN TablaOperarios op ON ln.OperarioId = op.Id
WHERE ln.OperarioId IS NOT NULL AND op.Id IS NULL

UNION ALL

SELECT 'Incidencias' as Tabla, COUNT(*) as RegistrosHuerfanos
FROM Incidencias inc
LEFT JOIN TablaOperarios op ON inc.CreadoPorId = op.Id
WHERE inc.CreadoPorId IS NOT NULL AND op.Id IS NULL

UNION ALL

SELECT 'TablaInspecciones' as Tabla, COUNT(*) as RegistrosHuerfanos
FROM TablaInspecciones ti
LEFT JOIN TablaOperarios op ON ti.RealizadoPorId = op.Id
WHERE ti.RealizadoPorId IS NOT NULL AND op.Id IS NULL

UNION ALL

SELECT 'TablaPesosEnvases' as Tabla, COUNT(*) as RegistrosHuerfanos
FROM TablaPesosEnvases tpe
LEFT JOIN TablaOperarios op ON tpe.RealizadoPorId = op.Id
WHERE tpe.RealizadoPorId IS NOT NULL AND op.Id IS NULL;

PRINT 'Limpieza de operarios duplicados completada.';
PRINT 'Verifica que no hay registros huérfanos en la consulta anterior.';
