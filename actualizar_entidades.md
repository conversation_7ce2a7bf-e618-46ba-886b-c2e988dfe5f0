# Guía para Actualizar las Entidades de Entity Framework

## 1. Regenerar Entidades desde la Base de Datos

Después de ejecutar el script de migración mejorado, necesitas regenerar las entidades de Entity Framework para que reflejen los cambios en la estructura de la base de datos.

### Comando para Regenerar
```bash
# Desde el directorio raíz del proyecto GestionAPQ_BLZ
dotnet ef dbcontext scaffold "Server=tu_servidor;Database=ApqLitalsa;Trusted_Connection=true;" Microsoft.EntityFrameworkCore.SqlServer -o Data/Entities/APQLitalsa -c APQLitalsaContext --context-dir Data/DbContexts/APQLitalsa --force
```

## 2. Cambios Esperados en las Entidades

### 2.1 TablaOperarios.cs (Nueva estructura)
```csharp
public partial class TablaOperarios
{
    public int Id { get; set; }
    public string Operario { get; set; }
    public bool Activo { get; set; } = true; // ✅ NUEVO
    public DateTime FechaCreacion { get; set; } // ✅ NUEVO
    public DateTime FechaModificacion { get; set; } // ✅ NUEVO
    
    // ✅ NUEVAS: Propiedades de navegación
    public virtual ICollection<Incidencias> IncidenciasCreadas { get; set; }
    public virtual ICollection<Lotesnodrizas> LotesNodrizas { get; set; }
    public virtual ICollection<TablaInspecciones> InspeccionesRealizadas { get; set; }
    public virtual ICollection<TablaPesosEnvases> PesosEnvasesRealizados { get; set; }
}
```

### 2.2 Incidencias.cs (Con FK a operarios)
```csharp
public partial class Incidencias
{
    public int Id { get; set; }
    public string Codigo { get; set; }
    public string Lote { get; set; }
    public DateTime FechaCreacion { get; set; }
    public int CreadoPorId { get; set; } // ✅ CAMBIADO: Era string CreadoPor
    public string Incidencia { get; set; }
    public string Ubicacion { get; set; }
    public string Estado { get; set; } // ✅ NUEVO
    public DateTime? FechaResolucion { get; set; } // ✅ NUEVO
    
    // ✅ NUEVA: Propiedad de navegación
    public virtual TablaOperarios CreadoPor { get; set; }
}
```

### 2.3 Lotesnodrizas.cs (Con FK a operarios)
```csharp
public partial class Lotesnodrizas
{
    public int Id { get; set; }
    public DateTime? Fecha { get; set; }
    public TimeSpan? Hora { get; set; }
    public int? Nodriza { get; set; }
    public int? Idproducto { get; set; }
    public string Lote { get; set; }
    public int? OperarioId { get; set; } // ✅ CAMBIADO: Era string Operario
    public string Ubicacion { get; set; }
    public DateTime FechaCreacion { get; set; } // ✅ NUEVO
    
    // ✅ NUEVA: Propiedad de navegación
    public virtual TablaOperarios Operario { get; set; }
}
```

### 2.4 TablaInspecciones.cs (Con FK a operarios)
```csharp
public partial class TablaInspecciones
{
    public int IdInspecciones { get; set; }
    public int? Idproducto { get; set; }
    public string Idlote { get; set; }
    public int? Viscosidad { get; set; }
    public decimal? Solidos { get; set; }
    public string ObservacionesAplicacion { get; set; }
    public bool Inspeccion { get; set; }
    public int? RealizadoPorId { get; set; } // ✅ CAMBIADO: Era string RealizadoPor
    public DateTime? Fecha { get; set; }
    public decimal? TemperaturaViscosidad { get; set; }
    public string ObservacionesQ { get; set; }
    
    // ✅ NUEVA: Propiedad de navegación
    public virtual TablaOperarios RealizadoPor { get; set; }
}
```

### 2.5 TablaPesosEnvases.cs (Con FK a operarios)
```csharp
public partial class TablaPesosEnvases
{
    public int Id { get; set; }
    public int? Numenvase { get; set; }
    public int? Idproducto { get; set; }
    public DateTime? Fecha { get; set; }
    public DateTime? FechaCaducidad { get; set; }
    public int? Pesoteoricoentrada { get; set; }
    public int? Pesorealentrada { get; set; }
    public int? Diferencia { get; set; }
    public int? Pesovacio { get; set; }
    public int? Pesoenvase { get; set; }
    public int? RealizadoPorId { get; set; } // ✅ CAMBIADO: Era string RealizadoPor
    public DateTime? FechaFin { get; set; }
    public string Ubicacion { get; set; }
    public string Lote { get; set; }
    public decimal? StockActual { get; set; }
    public string Observaciones { get; set; }
    
    // ✅ NUEVA: Propiedad de navegación
    public virtual TablaOperarios RealizadoPor { get; set; }
}
```

## 3. Actualizar DTOs

### 3.1 TablaOperariosDTO.cs (Nuevo)
```csharp
public class TablaOperariosDTO
{
    public int Id { get; set; }
    public string Operario { get; set; }
    public bool Activo { get; set; } = true; // ✅ NUEVO
    public DateTime FechaCreacion { get; set; } // ✅ NUEVO
    public DateTime FechaModificacion { get; set; } // ✅ NUEVO
}
```

### 3.2 IncidenciaDTO.cs (Actualizado)
```csharp
public class IncidenciaDTO
{
    public int Id { get; set; }
    public string Codigo { get; set; }
    public string Lote { get; set; }
    public DateTime FechaCreacion { get; set; }
    public int CreadoPorId { get; set; } // ✅ CAMBIADO
    public string CreadoPorNombre { get; set; } // ✅ NUEVO: Para mostrar el nombre
    public string Incidencia { get; set; }
    public string Ubicacion { get; set; }
    public string Estado { get; set; } // ✅ NUEVO
    public DateTime? FechaResolucion { get; set; } // ✅ NUEVO
}
```

### 3.3 LotesNodrizasDTO.cs (Actualizado)
```csharp
public class LotesNodrizasDTO
{
    public int Id { get; set; }
    public DateTime? Fecha { get; set; }
    public TimeSpan? Hora { get; set; }
    public int? Nodriza { get; set; }
    public int? Idproducto { get; set; }
    public string Lote { get; set; }
    public int? OperarioId { get; set; } // ✅ CAMBIADO
    public string OperarioNombre { get; set; } // ✅ NUEVO: Para mostrar el nombre
    public string Ubicacion { get; set; }
    public DateTime FechaCreacion { get; set; } // ✅ NUEVO
}
```

## 4. Actualizar Mapeos en AutoMapper

### 4.1 Actualizar DependencyInjection.cs
```csharp
private static void SetMapeos()
{
    // Mapeos existentes...
    
    // ✅ NUEVOS mapeos con configuración personalizada
    CreateMap<Incidencias, IncidenciaDTO>()
        .ForMember(dest => dest.CreadoPorNombre, opt => opt.MapFrom(src => src.CreadoPor.Operario));
    
    CreateMap<IncidenciaDTO, Incidencias>()
        .ForMember(dest => dest.CreadoPor, opt => opt.Ignore()); // Ignorar la navegación
    
    CreateMap<Lotesnodrizas, LotesNodrizasDTO>()
        .ForMember(dest => dest.OperarioNombre, opt => opt.MapFrom(src => src.Operario.Operario));
    
    CreateMap<LotesNodrizasDTO, Lotesnodrizas>()
        .ForMember(dest => dest.Operario, opt => opt.Ignore()); // Ignorar la navegación
    
    CreateMap<TablaInspecciones, TablaInspeccionesDTO>()
        .ForMember(dest => dest.RealizadoPorNombre, opt => opt.MapFrom(src => src.RealizadoPor.Operario));
    
    CreateMap<TablaPesosEnvases, TablaPesosEnvasesDTO>()
        .ForMember(dest => dest.RealizadoPorNombre, opt => opt.MapFrom(src => src.RealizadoPor.Operario));
    
    // ✅ NUEVO mapeo para TablaOperarios
    SetMapeoEstandar<TablaOperarios, TablaOperariosDTO>();
}
```

## 5. Actualizar Repositorios

### 5.1 Ejemplo: IncidenciasRepo.cs
```csharp
public class IncidenciasRepo : Repository<Incidencias, APQLitalsaContext>, IIncidenciasRepo
{
    public IncidenciasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    // ✅ NUEVO: Incluir operario en las consultas
    public async Task<List<Incidencias>> GetIncidenciasConOperario(bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetAll(asNoTracking, cancellationToken, include: query => query.Include(i => i.CreadoPor));
    }

    // ✅ ACTUALIZADO: Usar el nuevo campo CreadoPorId
    public async Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(string codigo, string lote,
        string ubicacion, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken,
            i => i.Codigo.Equals(codigo) && i.Lote.Equals(lote) && i.Ubicacion.Equals(ubicacion),
            include: query => query.Include(i => i.CreadoPor)); // ✅ Incluir operario
    }
}
```

## 6. Crear Nuevo Repositorio para Operarios

### 6.1 ITablaOperariosRepo.cs
```csharp
public interface ITablaOperariosRepo : IRepository<TablaOperarios>
{
    Task<List<TablaOperarios>> GetOperariosActivos(bool asNoTracking, CancellationToken cancellationToken);
    Task<TablaOperarios?> GetOperarioPorNombre(string nombre, bool asNoTracking, CancellationToken cancellationToken);
    Task DesactivarOperario(int operarioId, CancellationToken cancellationToken);
}
```

### 6.2 TablaOperariosRepo.cs
```csharp
public class TablaOperariosRepo : Repository<TablaOperarios, APQLitalsaContext>, ITablaOperariosRepo
{
    public TablaOperariosRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<TablaOperarios>> GetOperariosActivos(bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetAll(asNoTracking, cancellationToken, filter: o => o.Activo);
    }

    public async Task<TablaOperarios?> GetOperarioPorNombre(string nombre, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, o => o.Operario.Equals(nombre));
    }

    public async Task DesactivarOperario(int operarioId, CancellationToken cancellationToken)
    {
        var operario = await GetById(operarioId, false, cancellationToken);
        if (operario != null)
        {
            operario.Activo = false;
            operario.FechaModificacion = DateTime.Now;
            await Update(operario, cancellationToken);
        }
    }
}
```

## 7. Registrar Nuevos Servicios

### 7.1 Actualizar DependencyInjection.cs
```csharp
public static void AddRepositories(this IServiceCollection services)
{
    // Repositorios existentes...
    
    // ✅ NUEVO repositorio
    services.AddScoped<ITablaOperariosRepo, TablaOperariosRepo>();
}
```

## 8. Pasos de Migración

1. **Ejecutar el script SQL** `scriptMigracion_mejorado.sql`
2. **Regenerar entidades** con el comando EF Core
3. **Actualizar DTOs** según los ejemplos anteriores
4. **Actualizar mapeos** en AutoMapper
5. **Actualizar repositorios** existentes
6. **Crear nuevo repositorio** para operarios
7. **Registrar servicios** nuevos
8. **Probar la aplicación** y ajustar según sea necesario

## 9. Consideraciones Importantes

- ✅ **Backup**: Siempre hacer backup antes de ejecutar el script
- ✅ **Testing**: Probar todas las funcionalidades después de la migración
- ✅ **Gradual**: Puedes usar las vistas de compatibilidad durante la transición
- ✅ **Validación**: Verificar que todos los datos se migraron correctamente

---

Esta guía te ayudará a actualizar completamente tu aplicación Blazor para aprovechar las mejoras en la estructura de la base de datos.
