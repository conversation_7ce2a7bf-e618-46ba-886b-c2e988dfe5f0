namespace GestionAPQ_BLZ.Services;

using System.Web;
using DevExpress.XtraReports.Services;
using DevExpress.XtraReports.UI;
using DevExpress.XtraReports.Web.ClientControls;
using GestionAQP_BLZ.Server.Reports;

public class CustomReportProviderAsync : IReportProviderAsync
{
    public async Task<XtraReport> GetReportAsync(string id, ReportProviderContext context)
    {
        // Parse the string with the report name and parameter values.
        var parts = id.Split('?');
        var reportName = parts[0];
        var parametersQueryString = parts.Length > 1 ? parts[1] : string.Empty;

        // Create a report instance.
        XtraReport report;

        if (reportName == "ReporteEtiqueta")
            report = new ReporteEtiqueta();
        else
            throw new FaultException(
                string.Format("Could not find report '{0}'.", reportName)
            );

        // Apply the parameter values to the report.
        if (!string.IsNullOrEmpty(parametersQueryString))
        {
            var parameters = HttpUtility.ParseQueryString(parametersQueryString);

            foreach (var parameterName in parameters.AllKeys)
                if (report.Parameters[parameterName] != null)
                {
                    var paramValue = parameters.Get(parameterName);
                    report.Parameters[parameterName].Value = Convert.ChangeType(
                        paramValue, report.Parameters[parameterName].Type);
                }

            foreach (var parameter in report.Parameters)
                parameter.Visible = false;
        }

        return await Task.FromResult(report);
    }
}