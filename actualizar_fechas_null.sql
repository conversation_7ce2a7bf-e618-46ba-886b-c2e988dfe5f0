-- Script para actualizar los registros específicos con FechaHora NULL
USE ApqLitalsa;

PRINT '=== ACTUALIZANDO REGISTROS CON FECHAHORA NULL ===';

-- Mostrar registros que se van a actualizar
SELECT 
    Id, 
    FechaHora, 
    NodrizaId, 
    IdProducto, 
    Lote, 
    OperarioId, 
    Ubicacion
FROM dbo.LotesNodrizas 
WHERE FechaHora IS NULL;

PRINT 'Registros con FechaHora NULL encontrados: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- Actualizar los registros específicos con una fecha por defecto
UPDATE dbo.LotesNodrizas 
SET FechaHora = CAST('1900-01-01 00:00:00' AS DATETIME2),
    FechaModificacion = GETDATE()
WHERE FechaHora IS NULL;

PRINT 'Registros actualizados: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- Verificar que ya no hay registros con FechaHora NULL
SELECT 
    'Registros con FechaHora NULL después de la actualización' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas 
WHERE FechaHora IS NULL;

-- Mostrar los registros actualizados
PRINT '';
PRINT '=== REGISTROS ACTUALIZADOS ===';
SELECT TOP 10
    Id, 
    FechaHora, 
    NodrizaId, 
    IdProducto, 
    Lote, 
    OperarioId, 
    Ubicacion,
    FechaModificacion
FROM dbo.LotesNodrizas 
WHERE FechaHora = CAST('1900-01-01 00:00:00' AS DATETIME2)
ORDER BY FechaModificacion DESC;

PRINT '';
PRINT 'Actualización completada. Ahora puedes cambiar la columna FechaHora a NOT NULL si lo deseas.';
PRINT 'Comando: ALTER TABLE dbo.LotesNodrizas ALTER COLUMN FechaHora DATETIME2 NOT NULL;';
