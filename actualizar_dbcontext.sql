-- Script para actualizar el DbContext con la nueva entidad Nodrizas
-- Este script debe ejecutarse después de haber creado la tabla Nodrizas

-- 1. Actualizar el archivo efpt.config.json para incluir la tabla Nodrizas
-- Editar el archivo: GestionAPQ_BLZ\Data\DbContexts\APQLitalsa\efpt.config.json
-- Añadir la siguiente entrada en el array "Tables":
/*
{
    "Name": "[dbo].[Nodrizas]",
    "ObjectType": 0
}
*/

-- 2. Regenerar el DbContext usando EF Core Power Tools
-- Abrir Visual Studio
-- Hacer clic derecho en el proyecto GestionAPQ_BLZ
-- Seleccionar "EF Core Power Tools" > "Reverse Engineer"
-- Seleccionar la conexión a la base de datos ApqLitalsa
-- Marcar la tabla "Nodrizas" en la lista de tablas
-- <PERSON><PERSON> clic en "OK"

-- 3. Actualizar el DependencyInjection.cs para registrar el repositorio
-- A<PERSON>dir las siguientes líneas en el método CompleteInfraestructureConfig:
/*
builder.Services.AddScoped<INodrizasRepo, NodrizasRepo>();
builder.Services.AddScoped<IRepository<Nodrizas>>(provider =>
    provider.GetRequiredService<INodrizasRepo>());
*/

-- 4. Añadir el mapeo en el método SetMapeos:
/*
SetMapeoEstandar<Nodrizas, NodrizaDTO>();
*/

-- 5. Crear la interfaz del repositorio
-- Crear el archivo: GestionAPQ_BLZ\Repositories\Base\APQLitalsa\INodrizasRepo.cs
/*
namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface INodrizasRepo : IRepository<Nodrizas>
{
    Task<Nodrizas> GetByNumNodrizaAsync(int numNodriza, bool asNoTracking, CancellationToken cancellationToken);
    Task<List<Nodrizas>> GetActivasAsync(bool asNoTracking, CancellationToken cancellationToken);
}
*/

-- 6. Crear la implementación del repositorio
-- Crear el archivo: GestionAPQ_BLZ\Repositories\APQLitalsa\NodrizasRepo.cs
/*
namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class NodrizasRepo : Repository<Nodrizas, APQLitalsaContext>, INodrizasRepo
{
    public NodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Nodrizas> GetByNumNodrizaAsync(int numNodriza, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, n => n.NumNodriza == numNodriza && !n.Borrada);
    }

    public async Task<List<Nodrizas>> GetActivasAsync(bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetAll(asNoTracking, cancellationToken, n => n.Activo && !n.Borrada);
    }
}
*/

-- 7. Crear un handler genérico para consultas
-- Añadir en DependencyInjection.cs:
/*
builder.Services
    .AddScoped<IRequestHandler<GetAllEntitiesQuery<Nodrizas, NodrizaDTO>,
            ListResult<NodrizaDTO>>,
        GetAllEntitiesQueryHandler<Nodrizas, NodrizaDTO>>();
*/

-- 8. Crear un comando para guardar nodrizas
-- Crear el archivo: GestionAPQ_BLZ\MediaTR\Command\GrabarNodrizaCommand.cs
/*
namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;

public record GrabarNodrizaCommand(NodrizaDTO Nodriza) : IRequest<Result>;
*/

-- 9. Crear el handler para el comando
-- Crear el archivo: GestionAPQ_BLZ\MediaTR\Command\Handler\GrabarNodrizaCommandHandler.cs
/*
namespace GestionAPQ_BLZ.MediaTR.Command.Handler;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;
using System.Threading;
using System.Threading.Tasks;

public class GrabarNodrizaCommandHandler : IRequestHandler<GrabarNodrizaCommand, Result>
{
    private readonly INodrizasRepo _nodrizasRepo;

    public GrabarNodrizaCommandHandler(INodrizasRepo nodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
    }

    public async Task<Result> Handle(GrabarNodrizaCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var nodriza = TinyMapper.Map<Nodrizas>(request.Nodriza);
            
            if (nodriza.Id == 0)
            {
                // Es una nueva nodriza
                nodriza.FechaCreacion = DateTime.Now;
                nodriza.FechaModificacion = DateTime.Now;
                await _nodrizasRepo.Add(nodriza, cancellationToken);
            }
            else
            {
                // Es una actualización
                nodriza.FechaModificacion = DateTime.Now;
                await _nodrizasRepo.Update(nodriza, cancellationToken);
            }

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Error al grabar la nodriza: {ex.Message}");
        }
    }
}
*/

-- 10. Crear una vista de compatibilidad para código existente
CREATE OR ALTER VIEW vw_TablaProductosNodrizas AS
SELECT
    Id,
    NumNodriza AS IdNodriza,
    IdProducto AS Idproducto,
    CAST(0 AS BIT) AS Obsoleto,
    NULL AS FechaRetirada,
    Activo
FROM dbo.Nodrizas
WHERE Borrada = 0;
