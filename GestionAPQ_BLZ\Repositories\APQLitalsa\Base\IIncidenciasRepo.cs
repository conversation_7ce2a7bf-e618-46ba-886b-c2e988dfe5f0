namespace GestionAPQ_BLZ.Repositories.APQLitalsa.Base;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface IIncidenciasRepo
{
    Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProducto(
        int idNodriza,
        int idProducto,
        LotesNodrizasQueryOptions options,
        CancellationToken cancellationToken = default);
    Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(
        string codigo,
        string lote,
        string ubicacion,
        bool asNoTracking,
        CancellationToken cancellationToken = default);
}