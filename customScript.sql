-- Script de migración mejorado de Access a SQL Server
-- Incluye relaciones entre tablas y mejoras estructurales
USE ApqLitalsa;

-- =====================================================
-- PASO 1: CREAR TABLAS CON ESTRUCTURA MEJORADA
-- =====================================================

-- Primero eliminamos las tablas existentes si existen (en orden correcto por dependencias)
IF OBJECT_ID('dbo.Incidencias', 'U') IS NOT NULL DROP TABLE dbo.Incidencias;
IF OBJECT_ID('dbo.TablaInspecciones', 'U') IS NOT NULL DROP TABLE dbo.TablaInspecciones;
IF OBJECT_ID('dbo.TablaPesosEnvases', 'U') IS NOT NULL DROP TABLE dbo.TablaPesosEnvases;
IF OBJECT_ID('dbo.LOTESNODRIZAS', 'U') IS NOT NULL DROP TABLE dbo.LOTESNODRIZAS;
IF OBJECT_ID('dbo.TablaProductosNodrizas', 'U') IS NOT NULL DROP TABLE dbo.TablaProductosNodrizas;
IF OBJECT_ID('dbo.TablaAPQ', 'U') IS NOT NULL DROP TABLE dbo.TablaAPQ;
IF OBJECT_ID('dbo.[Switchboard Items]', 'U') IS NOT NULL DROP TABLE dbo.[Switchboard Items];
IF OBJECT_ID('dbo.TablaOperarios', 'U') IS NOT NULL DROP TABLE dbo.TablaOperarios;

-- =====================================================
-- TABLA MAESTRA: TablaOperarios (mejorada)
-- =====================================================
CREATE TABLE dbo.TablaOperarios (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Operario NVARCHAR(100) NOT NULL,
    Activo BIT NOT NULL DEFAULT 1, -- Campo añadido para activo/inactivo
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE()
);

-- =====================================================
-- TABLA: LOTESNODRIZAS (mejorada con FK a operarios)
-- =====================================================
CREATE TABLE dbo.LOTESNODRIZAS (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FECHA DATETIME2,
    HORA TIME,
    NODRIZA INT,
    IDPRODUCTO INT,
    LOTE NVARCHAR(50),
    OperarioId INT, -- Cambiado de OPERARIO string a FK
    Ubicacion NVARCHAR(100),
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    
    -- Foreign Key
    CONSTRAINT FK_LotesNodrizas_Operario FOREIGN KEY (OperarioId) 
        REFERENCES dbo.TablaOperarios(Id)
);

-- =====================================================
-- TABLA: Incidencias (mejorada con FK a operarios)
-- =====================================================
CREATE TABLE dbo.Incidencias (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Codigo NVARCHAR(50) NOT NULL,
    Lote NVARCHAR(50) NOT NULL,
    FechaCreacion DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreadoPorId INT NOT NULL, -- Cambiado de CreadoPor string a FK
    Incidencia NVARCHAR(MAX) NOT NULL,
    Ubicacion NVARCHAR(50) NOT NULL,
    Estado NVARCHAR(20) DEFAULT 'Abierta', -- Campo añadido para estado
    FechaResolucion DATETIME2,
    
    -- Foreign Key
    CONSTRAINT FK_Incidencias_CreadoPor FOREIGN KEY (CreadoPorId) 
        REFERENCES dbo.TablaOperarios(Id)
);

-- =====================================================
-- TABLA: TablaInspecciones (mejorada con FK a operarios)
-- =====================================================
CREATE TABLE dbo.TablaInspecciones (
    IdInspecciones INT IDENTITY(1,1) PRIMARY KEY,
    Idproducto INT,
    Idlote NVARCHAR(255),
    Viscosidad INT,
    Solidos DECIMAL(6,2),
    ObservacionesAplicacion NVARCHAR(255),
    Inspeccion BIT NOT NULL DEFAULT 0, -- OK/NO OK
    RealizadoPorId INT, -- Cambiado de RealizadoPor string a FK
    Fecha DATETIME2,
    TemperaturaViscosidad DECIMAL(6,2),
    ObservacionesQ NVARCHAR(255),
    
    -- Foreign Key
    CONSTRAINT FK_TablaInspecciones_RealizadoPor FOREIGN KEY (RealizadoPorId) 
        REFERENCES dbo.TablaOperarios(Id)
);

-- =====================================================
-- TABLA: TablaPesosEnvases (mejorada con FK a operarios)
-- =====================================================
CREATE TABLE dbo.TablaPesosEnvases (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    NUMENVASE INT,
    Idproducto INT,
    Fecha DATETIME2,
    FechaCaducidad DATETIME2,
    PESOTEORICOENTRADA INT,
    PESOREALENTRADA INT,
    DIFERENCIA INT,
    PESOVACIO INT,
    PESOENVASE INT,
    RealizadoPorId INT, -- Cambiado de RealizadoPor string a FK
    FechaFin DATETIME2,
    Ubicacion NVARCHAR(100),
    Lote NVARCHAR(50),
    StockActual DECIMAL(10,2),
    Observaciones NVARCHAR(MAX),
    
    -- Foreign Key
    CONSTRAINT FK_TablaPesosEnvases_RealizadoPor FOREIGN KEY (RealizadoPorId) 
        REFERENCES dbo.TablaOperarios(Id)
);

-- =====================================================
-- TABLA: TablaProductosNodrizas (sin cambios mayores)
-- =====================================================
CREATE TABLE dbo.TablaProductosNodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    IdNodriza INT,
    Idproducto INT,
    Obsoleto BIT NOT NULL DEFAULT 0,
    FechaRetirada DATETIME2,
    Activo BIT NOT NULL DEFAULT 1
);

-- =====================================================
-- TABLA: TablaAPQ (sin cambios mayores)
-- =====================================================
CREATE TABLE dbo.TablaAPQ (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    IdPosición NVARCHAR(255),
    Observacion NVARCHAR(255)
);

-- =====================================================
-- TABLA: Switchboard Items (mantenida para compatibilidad)
-- =====================================================
CREATE TABLE dbo.[Switchboard Items] (
    SwitchboardID INT,
    ItemNumber INT,
    ItemText NVARCHAR(255),
    Command INT,
    Argument NVARCHAR(255)
);

-- =====================================================
-- PASO 2: MIGRACIÓN DE DATOS
-- =====================================================

-- Migrar operarios primero (tabla maestra)
INSERT INTO dbo.TablaOperarios (Operario, Activo)
SELECT DISTINCT 
    Operario,
    1 as Activo -- Por defecto todos activos
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',  
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  
    'SELECT * FROM [TablaOperarios]'
);

-- Añadir operarios que aparecen en otras tablas pero no están en TablaOperarios
INSERT INTO dbo.TablaOperarios (Operario, Activo)
SELECT DISTINCT 
    OPERARIO,
    1 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',  
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  
    'SELECT DISTINCT OPERARIO FROM [LotesNodrizas] WHERE OPERARIO IS NOT NULL'
)
WHERE OPERARIO NOT IN (SELECT Operario FROM dbo.TablaOperarios);

-- Añadir operarios de RealizadoPor en TablaInspecciones
INSERT INTO dbo.TablaOperarios (Operario, Activo)
SELECT DISTINCT
    RealizadoPor,
    1 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT RealizadoPor FROM [TablaInspecciones] WHERE RealizadoPor IS NOT NULL'
)
WHERE RealizadoPor NOT IN (SELECT Operario FROM dbo.TablaOperarios);

-- Añadir operarios de RealizadoPor en TablaPesosEnvases
INSERT INTO dbo.TablaOperarios (Operario, Activo)
SELECT DISTINCT
    RealizadoPor,
    1 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT RealizadoPor FROM [TablaPesosEnvases] WHERE RealizadoPor IS NOT NULL'
)
WHERE RealizadoPor NOT IN (SELECT Operario FROM dbo.TablaOperarios);

-- Añadir operarios de CreadoPor en Incidencias
INSERT INTO dbo.TablaOperarios (Operario, Activo)
SELECT DISTINCT
    CreadoPor,
    1 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT CreadoPor FROM [Incidencias] WHERE CreadoPor IS NOT NULL'
)
WHERE CreadoPor NOT IN (SELECT Operario FROM dbo.TablaOperarios);

-- =====================================================
-- MIGRAR LOTESNODRIZAS con FK a operarios
-- =====================================================
INSERT INTO dbo.LOTESNODRIZAS (
    FECHA, HORA, NODRIZA, IDPRODUCTO, LOTE, OperarioId, Ubicacion
)
SELECT
    ln.FECHA,
    ln.HORA,
    ln.NODRIZA,
    ln.IDPRODUCTO,
    ln.LOTE,
    op.Id as OperarioId, -- Usar FK en lugar de string
    ln.UBICACIÓN
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
LEFT JOIN dbo.TablaOperarios op ON ln.OPERARIO = op.Operario;

-- =====================================================
-- MIGRAR Incidencias con FK a operarios
-- =====================================================
INSERT INTO dbo.Incidencias (
    Codigo, Lote, FechaCreacion, CreadoPorId, Incidencia, Ubicacion
)
SELECT
    inc.Codigo,
    inc.Lote,
    inc.FechaCreacion,
    op.Id as CreadoPorId, -- Usar FK en lugar de string
    inc.Incidencia,
    inc.Ubicacion
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [Incidencias]'
) inc
LEFT JOIN dbo.TablaOperarios op ON inc.CreadoPor = op.Operario;

-- =====================================================
-- MIGRAR TablaInspecciones con FK a operarios
-- =====================================================
INSERT INTO dbo.TablaInspecciones (
    Idproducto, Idlote, Viscosidad, Solidos, ObservacionesAplicacion,
    Inspeccion, RealizadoPorId, Fecha, TemperaturaViscosidad, ObservacionesQ
)
SELECT
    ti.IdProducto,
    ti.IdLote,
    ti.Viscosidad,
    ti.Solidos,
    ti.ObservacionesAplicación,
    CASE WHEN ti.[Inspección OK/NO OK] = 'OK' THEN 1 ELSE 0 END,
    op.Id as RealizadoPorId, -- Usar FK en lugar de string
    ti.Fecha,
    ti.TemperaturaViscosidad,
    ti.ObservacionesQ
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaInspecciones]'
) ti
LEFT JOIN dbo.TablaOperarios op ON ti.RealizadoPor = op.Operario;

-- =====================================================
-- MIGRAR TablaPesosEnvases con FK a operarios
-- =====================================================
INSERT INTO dbo.TablaPesosEnvases (
    NUMENVASE, Idproducto, Fecha, PESOTEORICOENTRADA, PESOREALENTRADA,
    DIFERENCIA, PESOVACIO, PESOENVASE, RealizadoPorId, FechaFin,
    Ubicacion, Lote, StockActual
)
SELECT
    tpe.NUMENVASE,
    tpe.Idproducto,
    tpe.Fecha,
    tpe.PESOTEORICOENTRADA,
    tpe.PESOREALENTRADA,
    tpe.DIFERENCIA,
    tpe.PESOVACIO,
    tpe.PESOENVASE,
    op.Id as RealizadoPorId, -- Usar FK en lugar de string
    tpe.FechaFin,
    tpe.Ubicacion,
    tpe.Lote,
    tpe.StockActual
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaPesosEnvases]'
) tpe
LEFT JOIN dbo.TablaOperarios op ON tpe.RealizadoPor = op.Operario;

-- =====================================================
-- MIGRAR TablaProductosNodrizas (sin cambios)
-- =====================================================
INSERT INTO dbo.TablaProductosNodrizas (
    IdNodriza, Idproducto, Obsoleto, FechaRetirada, Activo
)
SELECT
    IdNodriza, IdProducto, Obsoleto, FechaRetirada, Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaProductosNodrizas]'
);

-- =====================================================
-- MIGRAR TablaAPQ (sin cambios)
-- =====================================================
INSERT INTO dbo.TablaAPQ (IdPosición, Observacion)
SELECT IdPosición, Observacion
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaAPQ]'
);

-- =====================================================
-- MIGRAR Switchboard Items (sin cambios)
-- =====================================================
INSERT INTO dbo.[Switchboard Items] (
    SwitchboardID, ItemNumber, ItemText, Command, Argument
)
SELECT
    [Switchboardid], ITEMNUMBER, ITEMTEXT, COMMAND, ARGUMENT
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [Switchboard Items]'
);

-- =====================================================
-- PASO 3: CREAR ÍNDICES PARA MEJORAR RENDIMIENTO
-- =====================================================

-- Índices en TablaOperarios
CREATE INDEX IX_TablaOperarios_Operario ON dbo.TablaOperarios(Operario);
CREATE INDEX IX_TablaOperarios_Activo ON dbo.TablaOperarios(Activo);

-- Índices en LOTESNODRIZAS
CREATE INDEX IX_LotesNodrizas_Fecha ON dbo.LOTESNODRIZAS(FECHA);
CREATE INDEX IX_LotesNodrizas_Lote ON dbo.LOTESNODRIZAS(LOTE);
CREATE INDEX IX_LotesNodrizas_Operario ON dbo.LOTESNODRIZAS(OperarioId);
CREATE INDEX IX_LotesNodrizas_Producto ON dbo.LOTESNODRIZAS(IDPRODUCTO);

-- Índices en Incidencias
CREATE INDEX IX_Incidencias_Codigo ON dbo.Incidencias(Codigo);
CREATE INDEX IX_Incidencias_Lote ON dbo.Incidencias(Lote);
CREATE INDEX IX_Incidencias_FechaCreacion ON dbo.Incidencias(FechaCreacion);
CREATE INDEX IX_Incidencias_CreadoPor ON dbo.Incidencias(CreadoPorId);
CREATE INDEX IX_Incidencias_Estado ON dbo.Incidencias(Estado);

-- Índices en TablaInspecciones
CREATE INDEX IX_TablaInspecciones_Producto ON dbo.TablaInspecciones(Idproducto);
CREATE INDEX IX_TablaInspecciones_Lote ON dbo.TablaInspecciones(Idlote);
CREATE INDEX IX_TablaInspecciones_Fecha ON dbo.TablaInspecciones(Fecha);
CREATE INDEX IX_TablaInspecciones_RealizadoPor ON dbo.TablaInspecciones(RealizadoPorId);

-- Índices en TablaPesosEnvases
CREATE INDEX IX_TablaPesosEnvases_Envase ON dbo.TablaPesosEnvases(NUMENVASE);
CREATE INDEX IX_TablaPesosEnvases_Producto ON dbo.TablaPesosEnvases(Idproducto);
CREATE INDEX IX_TablaPesosEnvases_Fecha ON dbo.TablaPesosEnvases(Fecha);
CREATE INDEX IX_TablaPesosEnvases_Lote ON dbo.TablaPesosEnvases(Lote);
CREATE INDEX IX_TablaPesosEnvases_RealizadoPor ON dbo.TablaPesosEnvases(RealizadoPorId);

-- =====================================================
-- PASO 4: VISTAS PARA COMPATIBILIDAD CON CÓDIGO EXISTENTE
-- =====================================================

-- Vista para mantener compatibilidad con consultas existentes de LOTESNODRIZAS
CREATE VIEW vw_LOTESNODRIZAS_Compatible AS
SELECT
    ln.Id,
    ln.FECHA,
    ln.HORA,
    ln.NODRIZA,
    ln.IDPRODUCTO,
    ln.LOTE,
    op.Operario as OPERARIO, -- Devolver el nombre del operario
    ln.Ubicacion,
    ln.FechaCreacion
FROM dbo.LOTESNODRIZAS ln
LEFT JOIN dbo.TablaOperarios op ON ln.OperarioId = op.Id;

-- Vista para mantener compatibilidad con consultas existentes de Incidencias
CREATE VIEW vw_Incidencias_Compatible AS
SELECT
    inc.Id,
    inc.Codigo,
    inc.Lote,
    inc.FechaCreacion,
    op.Operario as CreadoPor, -- Devolver el nombre del operario
    inc.Incidencia,
    inc.Ubicacion,
    inc.Estado,
    inc.FechaResolucion
FROM dbo.Incidencias inc
LEFT JOIN dbo.TablaOperarios op ON inc.CreadoPorId = op.Id;

-- Vista para mantener compatibilidad con consultas existentes de TablaInspecciones
CREATE VIEW vw_TablaInspecciones_Compatible AS
SELECT
    ti.IdInspecciones,
    ti.Idproducto,
    ti.Idlote,
    ti.Viscosidad,
    ti.Solidos,
    ti.ObservacionesAplicacion,
    ti.Inspeccion,
    op.Operario as RealizadoPor, -- Devolver el nombre del operario
    ti.Fecha,
    ti.TemperaturaViscosidad,
    ti.ObservacionesQ
FROM dbo.TablaInspecciones ti
LEFT JOIN dbo.TablaOperarios op ON ti.RealizadoPorId = op.Id;

-- Vista para mantener compatibilidad con consultas existentes de TablaPesosEnvases
CREATE VIEW vw_TablaPesosEnvases_Compatible AS
SELECT
    tpe.Id,
    tpe.NUMENVASE,
    tpe.Idproducto,
    tpe.Fecha,
    tpe.FechaCaducidad,
    tpe.PESOTEORICOENTRADA,
    tpe.PESOREALENTRADA,
    tpe.DIFERENCIA,
    tpe.PESOVACIO,
    tpe.PESOENVASE,
    op.Operario as RealizadoPor, -- Devolver el nombre del operario
    tpe.FechaFin,
    tpe.Ubicacion,
    tpe.Lote,
    tpe.StockActual,
    tpe.Observaciones
FROM dbo.TablaPesosEnvases tpe
LEFT JOIN dbo.TablaOperarios op ON tpe.RealizadoPorId = op.Id;

-- =====================================================
-- PASO 5: PROCEDIMIENTOS ALMACENADOS ÚTILES
-- =====================================================

-- Procedimiento para desactivar un operario
CREATE PROCEDURE sp_DesactivarOperario
    @OperarioId INT
AS
BEGIN
    UPDATE dbo.TablaOperarios
    SET Activo = 0, FechaModificacion = GETDATE()
    WHERE Id = @OperarioId;
END;

-- Procedimiento para obtener incidencias por operario
CREATE PROCEDURE sp_GetIncidenciasPorOperario
    @OperarioId INT = NULL,
    @FechaDesde DATETIME2 = NULL,
    @FechaHasta DATETIME2 = NULL
AS
BEGIN
    SELECT
        inc.*,
        op.Operario as CreadoPor
    FROM dbo.Incidencias inc
    LEFT JOIN dbo.TablaOperarios op ON inc.CreadoPorId = op.Id
    WHERE (@OperarioId IS NULL OR inc.CreadoPorId = @OperarioId)
      AND (@FechaDesde IS NULL OR inc.FechaCreacion >= @FechaDesde)
      AND (@FechaHasta IS NULL OR inc.FechaCreacion <= @FechaHasta)
    ORDER BY inc.FechaCreacion DESC;
END;

-- =====================================================
-- SCRIPT COMPLETADO
-- =====================================================

PRINT 'Migración completada exitosamente.';
PRINT 'Se han creado las siguientes mejoras:';
PRINT '1. Campo Activo añadido a TablaOperarios';
PRINT '2. Foreign Keys creadas para todos los campos de operarios';
PRINT '3. Índices creados para mejorar rendimiento';
PRINT '4. Vistas de compatibilidad creadas';
PRINT '5. Procedimientos almacenados útiles añadidos';
PRINT '';
PRINT 'IMPORTANTE: Actualizar las entidades de Entity Framework para reflejar los cambios.';
