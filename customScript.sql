-- Script de migración mejorado de Access a SQL Server
-- Incluye relaciones entre tablas y mejoras estructurales
USE ApqLitalsa;

-- =====================================================
-- PASO 1: CREAR TABLAS
-- =====================================================

-- Primero eliminamos las tablas existentes si existen
IF OBJECT_ID('dbo.Incidencias', 'U') IS NOT NULL DROP TABLE dbo.Incidencias;
IF OBJECT_ID('dbo.LotesNodrizas', 'U') IS NOT NULL DROP TABLE dbo.LotesNodrizas;
IF OBJECT_ID('dbo.Nodrizas', 'U') IS NOT NULL DROP TABLE dbo.Nodrizas;
IF OBJECT_ID('dbo.Operarios', 'U') IS NOT NULL DROP TABLE dbo.Operarios;

-- =====================================================
-- TABLA MAESTRA: Operarios
-- =====================================================
CREATE TABLE dbo.Operarios (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Nombre NVARCHAR(50) NOT NULL,
    Apellido1 NVARCHAR(50),
    Apellido2 NVARCHAR(50),
    Activo BIT NOT NULL DEFAULT 1, -- Campo añadido para activo/inactivo
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE()
);

-- =====================================================
-- TABLA: Nodrizas (antes TablaProductosNodrizas)
-- =====================================================
CREATE TABLE dbo.Nodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    NumNodriza INT,
    IdProducto INT,
    Activo BIT NOT NULL DEFAULT 1,
    Borrada BIT NOT NULL DEFAULT 0, -- Todas quedarán a 0 inicialmente
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),

    -- Índices para mejorar rendimiento
    INDEX IX_Nodrizas_NumNodriza (NumNodriza),
    INDEX IX_Nodrizas_IdProducto (IdProducto),
    INDEX IX_Nodrizas_Activo (Activo),
    INDEX IX_Nodrizas_Borrada (Borrada)
);

-- =====================================================
-- TABLA: LotesNodrizas (mejorada con FK y fecha unificada)
-- =====================================================
CREATE TABLE dbo.LotesNodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Fecha DATETIME2 NOT NULL,
    IdNodriza INT,
    IdProducto INT NOT NULL,
    Lote NVARCHAR(50),
    IdOperario INT NOT NULL,
    Ubicacion NVARCHAR(100),
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),

    -- Foreign Keys
    CONSTRAINT FK_LotesNodrizas_Nodriza FOREIGN KEY (IdNodriza)
        REFERENCES dbo.Nodrizas(Id),
    CONSTRAINT FK_LotesNodrizas_Operario FOREIGN KEY (IdOperario)
        REFERENCES dbo.Operarios(Id),

    -- Índices para mejorar rendimiento
    INDEX IX_LotesNodrizas_IdNodriza (IdNodriza),
    INDEX IX_LotesNodrizas_IdProducto (IdProducto),
    INDEX IX_LotesNodrizas_IdOperario (IdOperario),
    INDEX IX_LotesNodrizas_Lote (Lote)
);

-- =====================================================
-- TABLA: Incidencias (mejorada con FK a Operarios)
-- =====================================================
CREATE TABLE dbo.Incidencias (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Codigo NVARCHAR(50) NOT NULL,           -- Código del producto
    Lote NVARCHAR(50) NOT NULL,             -- Lote
    Ubicacion NVARCHAR(100) NOT NULL,       -- Ubicación
    Incidencia NVARCHAR(MAX) NOT NULL,      -- Descripción de la incidencia
    IdOperario INT NOT NULL,                -- ✅ FK a Operarios (reemplaza creadoPor)
    FechaCreacion DATETIME2 DEFAULT GETDATE(),    -- ✅ Nueva columna
    FechaModificacion DATETIME2 DEFAULT GETDATE(), -- ✅ Nueva columna

    -- Foreign Key
    CONSTRAINT FK_Incidencias_Operario FOREIGN KEY (IdOperario)
        REFERENCES dbo.Operarios(Id),

    -- Índices para mejorar rendimiento
    INDEX IX_Incidencias_Codigo (Codigo),
    INDEX IX_Incidencias_Lote (Lote),
    INDEX IX_Incidencias_Ubicacion (Ubicacion),
    INDEX IX_Incidencias_IdOperario (IdOperario),

    -- Constraint único para evitar duplicados
    CONSTRAINT UK_Incidencias_Codigo_Lote_Ubicacion UNIQUE (Codigo, Lote, Ubicacion)
);

-- =====================================================
-- PASO 3: SP's PARA NORMALIZAR TEXTOS
-- =====================================================
-- Función para limpiar texto (eliminar saltos de línea, espacios múltiples, etc.)
-- CREATE OR ALTER FUNCTION dbo.LimpiarTexto(@texto NVARCHAR(255))
-- RETURNS NVARCHAR(255)
-- AS
-- BEGIN
    -- DECLARE @resultado NVARCHAR(255);

    -- -- Si es NULL o vacío, devolver NULL
    -- IF @texto IS NULL OR LTRIM(RTRIM(@texto)) = ''
        -- RETURN NULL;

    -- -- Limpiar el texto
    -- SET @resultado = @texto;

    -- -- Eliminar saltos de línea, tabulaciones y caracteres de control
    -- SET @resultado = REPLACE(@resultado, CHAR(13), ''); -- Carriage Return
    -- SET @resultado = REPLACE(@resultado, CHAR(10), ''); -- Line Feed
    -- SET @resultado = REPLACE(@resultado, CHAR(9), '');  -- Tab
    -- SET @resultado = REPLACE(@resultado, CHAR(160), ' '); -- Non-breaking space

    -- -- Eliminar espacios múltiples y trim
    -- WHILE CHARINDEX('  ', @resultado) > 0
        -- SET @resultado = REPLACE(@resultado, '  ', ' ');

    -- SET @resultado = LTRIM(RTRIM(@resultado));

    -- -- Si después de limpiar queda vacío, devolver NULL
    -- IF @resultado = ''
        -- RETURN NULL;

    -- RETURN @resultado;
-- END;

-- -- Función para normalizar nombres específicos
-- CREATE OR ALTER FUNCTION dbo.NormalizarNombre(@nombre NVARCHAR(50))
-- RETURNS NVARCHAR(50)
-- AS
-- BEGIN
    -- -- Normalizar casos específicos conocidos
    -- RETURN CASE
        -- WHEN @nombre LIKE '%Raul%' OR @nombre LIKE '%Raúl%' THEN 'Raúl'
        -- WHEN @nombre LIKE '%Kike%' THEN 'Kike'
        -- WHEN @nombre LIKE '%Javier%' THEN 'Javier'
        -- WHEN @nombre LIKE '%Miguel%' OR @nombre LIKE '%Migurl%' THEN 'Miguel'
        -- WHEN @nombre LIKE '%Alejandro%' THEN 'Alejandro'
        -- WHEN @nombre LIKE '%Alvaro%' OR @nombre LIKE '%Álvaro%' THEN 'Álvaro'
        -- WHEN @nombre LIKE '%Mateo%' THEN 'Mateo'
        -- WHEN @nombre LIKE '%Jesus%' OR @nombre LIKE '%Jesús%' THEN 'Jesús'
        -- WHEN @nombre LIKE '%Oscar%' OR @nombre LIKE '%Óscar%' THEN 'Óscar'
        -- WHEN @nombre LIKE '%Andres%' OR @nombre LIKE '%Andrés%' THEN 'Andrés'
        -- ELSE @nombre
    -- END;
-- END;

-- -- Función para separar nombre completo en partes
-- CREATE OR ALTER FUNCTION dbo.SepararNombreCompleto(@nombreCompleto NVARCHAR(255))
-- RETURNS TABLE
-- AS
-- RETURN (
    -- SELECT
        -- CASE
            -- -- Casos especiales conocidos
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Álvaro Gonzalez' THEN 'Álvaro'
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Jesus Carra' THEN 'Jesús'
            -- -- Caso general: primera palabra es el nombre
            -- ELSE dbo.NormalizarNombre(
                    -- CASE
                        -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) > 0
                        -- THEN LEFT(dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) - 1)
                        -- ELSE dbo.LimpiarTexto(@nombreCompleto)
                    -- END
                 -- )
        -- END AS Nombre,

        -- CASE
            -- -- Casos especiales conocidos
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Álvaro Gonzalez' THEN 'González'
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Jesus Carra' THEN 'Carra'
            -- -- Caso general: segunda palabra es el primer apellido
            -- ELSE CASE
                    -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) > 0
                    -- THEN
                        -- CASE
                            -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) > 0
                            -- THEN SUBSTRING(
                                    -- dbo.LimpiarTexto(@nombreCompleto),
                                    -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1,
                                    -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) - CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) - 1
                                 -- )
                            -- ELSE SUBSTRING(
                                    -- dbo.LimpiarTexto(@nombreCompleto),
                                    -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1,
                                    -- LEN(dbo.LimpiarTexto(@nombreCompleto))
                                 -- )
                        -- END
                    -- ELSE NULL
                -- END
        -- END AS Apellido1,

        -- CASE
            -- -- Casos especiales conocidos
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Álvaro Gonzalez' THEN NULL
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Jesus Carra' THEN NULL
            -- -- Caso general: tercera palabra en adelante es el segundo apellido
            -- ELSE CASE
                    -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) > 0
                    -- THEN SUBSTRING(
                            -- dbo.LimpiarTexto(@nombreCompleto),
                            -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) + 1,
                            -- LEN(dbo.LimpiarTexto(@nombreCompleto))
                         -- )
                    -- ELSE NULL
                -- END
        -- END AS Apellido2
-- );

-- =====================================================
-- PASO 2: MIGRACIÓN DE DATOS
-- =====================================================
-- Migrar operarios primero (tabla maestra)
-- Insertar operario DESCONOCIDO si no existe
IF NOT EXISTS (SELECT 1 FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
BEGIN
    INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
    VALUES ('DESCONOCIDO', NULL, NULL, 0); -- Inactivo por defecto
END;

INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    1 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaOperarios]'
) op
CROSS APPLY dbo.SepararNombreCompleto(op.Operario) sep
WHERE dbo.LimpiarTexto(Operario) IS NOT NULL;

-- Añadir operarios que aparecen en otras tablas pero no están en Operarios
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    0 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT OPERARIO FROM [LotesNodrizas] WHERE OPERARIO IS NOT NULL'
) ln
CROSS APPLY dbo.SepararNombreCompleto(ln.OPERARIO) sep
WHERE dbo.LimpiarTexto(OPERARIO) IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM dbo.Operarios
      WHERE Nombre = sep.Nombre
        AND ISNULL(Apellido1,'') = ISNULL(sep.Apellido1,'')
  );

-- Añadir operarios de RealizadoPor en TablaInspecciones
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    0 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT RealizadoPor FROM [TablaInspecciones] WHERE RealizadoPor IS NOT NULL'
) ti
CROSS APPLY dbo.SepararNombreCompleto(ti.RealizadoPor) sep
WHERE dbo.LimpiarTexto(RealizadoPor) IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM dbo.Operarios
      WHERE Nombre = sep.Nombre
        AND ISNULL(Apellido1,'') = ISNULL(sep.Apellido1,'')
  );

-- Añadir operarios de RealizadoPor en TablaPesosEnvases
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    0 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT RealizadoPor FROM [TablaPesosEnvases] WHERE RealizadoPor IS NOT NULL'
) tpe
CROSS APPLY dbo.SepararNombreCompleto(tpe.RealizadoPor) sep
WHERE dbo.LimpiarTexto(RealizadoPor) IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM dbo.Operarios
      WHERE Nombre = sep.Nombre
        AND ISNULL(Apellido1,'') = ISNULL(sep.Apellido1,'')
  );



-- =====================================================
-- MIGRAR DATOS DE TablaProductosNodrizas A Nodrizas
-- =====================================================
INSERT INTO dbo.Nodrizas (NumNodriza, IdProducto, Activo, Borrada)
SELECT
    IdNodriza,
    CASE WHEN Idproducto = 0 THEN NULL ELSE Idproducto END as Idproducto,
    Activo,
    0 -- Todas las nodrizas se migran como no borradas
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaProductosNodrizas] WHERE IdNodriza IS NOT NULL AND Idproducto IS NOT NULL'
)
WHERE IdNodriza IS NOT NULL AND Idproducto IS NOT NULL;



-- =====================================================
-- MIGRAR DATOS DE LotesNodrizas (con FK y fecha unificada)
-- =====================================================
-- Crear tabla temporal con datos ordenados y fechas interpoladas
INSERT INTO dbo.LotesNodrizas (Fecha, IdNodriza, IdProducto, Lote, IdOperario, Ubicacion)
SELECT 
    CASE
        WHEN ln.FECHA IS NOT NULL AND ln.HORA IS NOT NULL THEN
            DATEADD(SECOND,
                DATEPART(HOUR, ln.HORA) * 3600 +
                DATEPART(MINUTE, ln.HORA) * 60 +
                DATEPART(SECOND, ln.HORA),
                CAST(ln.FECHA AS DATETIME2))
        WHEN ln.FECHA IS NOT NULL THEN
            CAST(ln.FECHA AS DATETIME2)
        ELSE
            CASE -- ID's con fecha a null.. las corregimos a mano
                WHEN ln.Id BETWEEN 464 AND 474 THEN CAST('2018-07-07 00:00:00' AS DATETIME2)
                WHEN ln.Id = 10312 THEN CAST('2021-06-07 07:15:00' AS DATETIME2)
                WHEN ln.Id = 10620 THEN CAST('2021-07-01 08:33:00' AS DATETIME2)
                -- Para otros registros NULL, usar una fecha por defecto
                ELSE CAST('2021-01-01 00:00:00' AS DATETIME2)
            END
    END as FechaHora,
    
    -- Buscar el ID de la nodriza (NULL si NODRIZA es NULL o 0)
    CASE 
        WHEN ln.NODRIZA IS NULL OR ln.NODRIZA = 0 THEN NULL 
        ELSE n.Id 
    END as NodrizaId,
    
    ln.IDPRODUCTO,
    ln.LOTE,
    
    -- Buscar el ID del operario (DESCONOCIDO si es NULL o no existe)
    ISNULL(
        CASE 
            WHEN ln.OPERARIO IS NULL OR LTRIM(RTRIM(ln.OPERARIO)) = '' THEN NULL
            ELSE op.Id
        END,
        (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
    ) as OperarioId,
    
    ln.[UBICACIÓN]
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''));

-- =====================================================
-- PASO 3: CREAR ÍNDICES PARA MEJORAR RENDIMIENTO
-- =====================================================

-- Índices en Operarios
CREATE INDEX IX_Operarios_Activo ON dbo.Operarios(Activo);