-- Script alternativo para migrar LotesNodrizas con manejo simplificado de fecha/hora
-- Usar este script si el principal da problemas con la conversión de fecha/hora

USE ApqLitalsa;

-- Opción 1: Migración con conversión simple de fecha/hora
INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT 
    -- Opción simple: convertir fecha a datetime y agregar hora como string
    CASE 
        WHEN ln.HORA IS NOT NULL AND ln.FECHA IS NOT NULL THEN 
            CAST(CAST(ln.FECHA AS DATE) AS VARCHAR(10)) + ' ' + CAST(ln.HORA AS VARCHAR(8))
        WHEN ln.FECHA IS NOT NULL THEN 
            CAST(ln.FECHA AS DATETIME2)
        ELSE 
            GETDATE()
    END as FechaHora,
    
    n.Id as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ln.IDPRODUCTO,
    ln.LOT<PERSON>,
    op.Id as <PERSON><PERSON><PERSON><PERSON>,
    ln.[UBICACIÓN]
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE NODRIZA IS NOT NULL AND OPERARIO IS NOT NULL'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE ln.NODRIZA IS NOT NULL 
  AND ln.OPERARIO IS NOT NULL 
  AND n.Id IS NOT NULL 
  AND op.Id IS NOT NULL;

-- Opción 2: Si la anterior no funciona, migrar solo con fecha y actualizar después
/*
-- Paso 1: Migrar solo con fecha
INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT 
    ISNULL(CAST(ln.FECHA AS DATETIME2), GETDATE()) as FechaHora,
    n.Id as NodrizaId,
    ln.IDPRODUCTO,
    ln.LOTE,
    op.Id as OperarioId,
    ln.[UBICACIÓN]
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE NODRIZA IS NOT NULL AND OPERARIO IS NOT NULL'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE ln.NODRIZA IS NOT NULL 
  AND ln.OPERARIO IS NOT NULL 
  AND n.Id IS NOT NULL 
  AND op.Id IS NOT NULL;

-- Paso 2: Actualizar con la hora (si es necesario)
-- Este paso se puede hacer después si se necesita mayor precisión
*/

-- Verificar los resultados
SELECT TOP 10
    Id,
    FechaHora,
    NodrizaId,
    IdProducto,
    Lote,
    OperarioId,
    Ubicacion
FROM dbo.LotesNodrizas
ORDER BY FechaHora DESC;

PRINT 'Migración de LotesNodrizas completada con script alternativo.';
