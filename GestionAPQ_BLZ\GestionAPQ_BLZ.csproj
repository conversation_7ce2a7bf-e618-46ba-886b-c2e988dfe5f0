﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\GestionAPQ_BLZ.Client\GestionAPQ_BLZ.Client.csproj" />
		<PackageReference Include="Blazr.RenderState.Server" Version="1.0.0" />
		<PackageReference Include="Common.ResponseModels" Version="1.0.0" />
		<PackageReference Include="DevExpress.Blazor" Version="24.2.8" />
		<PackageReference Include="DevExpress.Blazor.Reporting.JSBasedControls" Version="24.2.8" />
		<PackageReference Include="DevExpress.AspNetCore.Reporting" Version="24.2.8" />
		<PackageReference Include="Logging.Shared" Version="1.0.0" />
		<PackageReference Include="MediatR" Version="13.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.18" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.18" />
		<PackageReference Include="TinyMapper" Version="3.0.3" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="Data\DbContexts\APQLitalsa\efpt.config.json.user" />
	  <None Include="Data\DbContexts\CalidadLitalsa\efpt.config.json.user" />
	  <None Include="Data\DbContexts\DatoLita01\efpt.config.json.user" />
	</ItemGroup>


	<ItemGroup>
	  <Folder Include="Data\Entities\DatoLita01\" />
	  <Folder Include="Reports\" />
	</ItemGroup>
</Project>
