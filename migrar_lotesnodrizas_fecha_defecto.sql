-- Script para migrar LotesNodrizas con fecha por defecto para registros NULL
USE ApqLitalsa;

-- =====================================================
-- CREAR TABLA LOTESNODRIZAS CON FECHAHORA NOT NULL
-- =====================================================
IF OBJECT_ID('dbo.LotesNodrizas', 'U') IS NOT NULL DROP TABLE dbo.LotesNodrizas;

CREATE TABLE dbo.LotesNodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FechaHora DATETIME2 NOT NULL,           -- ✅ NOT NULL desde el inicio
    NodrizaId INT NULL,                     -- FK a Nodrizas (NULL si NODRIZA=0 o NULL)
    IdProducto INT NOT NULL,
    Lote NVARCHAR(50),
    OperarioId INT NOT NULL,                -- FK a Operarios
    Ubicacion NVARCHAR(100),
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),
    
    -- Índices
    INDEX IX_LotesNodrizas_FechaHora (FechaHora),
    INDEX IX_LotesNodrizas_NodrizaId (NodrizaId),
    INDEX IX_LotesNodrizas_OperarioId (OperarioId),
    INDEX IX_LotesNodrizas_IdProducto (IdProducto),
    
    -- Foreign Keys
    CONSTRAINT FK_LotesNodrizas_Nodrizas FOREIGN KEY (NodrizaId) REFERENCES dbo.Nodrizas(Id),
    CONSTRAINT FK_LotesNodrizas_Operarios FOREIGN KEY (OperarioId) REFERENCES dbo.Operarios(Id)
);

-- =====================================================
-- CREAR OPERARIO DESCONOCIDO SI NO EXISTE
-- =====================================================
IF NOT EXISTS (SELECT 1 FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
BEGIN
    INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
    VALUES ('DESCONOCIDO', NULL, NULL, 0);
    PRINT 'Operario DESCONOCIDO creado con ID: ' + CAST(SCOPE_IDENTITY() AS NVARCHAR(10));
END
ELSE
BEGIN
    DECLARE @DesconocidoId INT = (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO');
    PRINT 'Operario DESCONOCIDO ya existe con ID: ' + CAST(@DesconocidoId AS NVARCHAR(10));
END

-- =====================================================
-- MIGRAR DATOS CON FECHA POR DEFECTO
-- =====================================================
PRINT 'Iniciando migración de LotesNodrizas...';

INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT 
    -- ✅ FECHAS ESPECÍFICAS PARA REGISTROS SEGÚN LA IMAGEN
    CASE
        WHEN ln.FECHA IS NOT NULL AND ln.HORA IS NOT NULL THEN
            DATEADD(SECOND,
                DATEPART(HOUR, ln.HORA) * 3600 +
                DATEPART(MINUTE, ln.HORA) * 60 +
                DATEPART(SECOND, ln.HORA),
                CAST(ln.FECHA AS DATETIME2))
        WHEN ln.FECHA IS NOT NULL THEN
            CAST(ln.FECHA AS DATETIME2)
        ELSE
            -- Asignar fechas específicas según el ID del registro
            CASE
                WHEN ln.Id = 10312 THEN CAST('2021-06-07 07:15:00' AS DATETIME2)
                WHEN ln.Id = 10620 THEN CAST('2021-07-01 08:33:00' AS DATETIME2)
                -- Para otros registros NULL, usar una fecha por defecto
                ELSE CAST('2021-01-01 00:00:00' AS DATETIME2)
            END
    END as FechaHora,
    
    -- Buscar el ID de la nodriza (NULL si NODRIZA es NULL o 0)
    CASE 
        WHEN ln.NODRIZA IS NULL OR ln.NODRIZA = 0 THEN NULL 
        ELSE n.Id 
    END as NodrizaId,
    
    ln.IDPRODUCTO,
    ln.LOTE,
    
    -- Buscar el ID del operario (DESCONOCIDO si es NULL o no existe)
    ISNULL(
        CASE 
            WHEN ln.OPERARIO IS NULL OR LTRIM(RTRIM(ln.OPERARIO)) = '' THEN NULL
            ELSE op.Id
        END,
        (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
    ) as OperarioId,
    
    ln.[UBICACIÓN]
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''));

PRINT 'Migración completada. Registros migrados: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =====================================================
-- VERIFICAR RESULTADOS
-- =====================================================
SELECT 
    'Total migrado' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas

UNION ALL

SELECT 
    'Con NodrizaId NULL' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas
WHERE NodrizaId IS NULL

UNION ALL

SELECT 
    'Con operario DESCONOCIDO' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas ln
INNER JOIN dbo.Operarios op ON ln.OperarioId = op.Id
WHERE op.Nombre = 'DESCONOCIDO'

UNION ALL

SELECT 
    'Con fecha por defecto (1900-01-01)' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas
WHERE FechaHora = CAST('1900-01-01 00:00:00' AS DATETIME2);

-- Mostrar algunos ejemplos de registros con fecha por defecto
PRINT '';
PRINT '=== REGISTROS CON FECHA POR DEFECTO ===';
SELECT TOP 10
    Id,
    FechaHora,
    CASE WHEN NodrizaId IS NULL THEN 'SIN NODRIZA' ELSE CAST(NodrizaId AS NVARCHAR(10)) END as NodrizaId,
    IdProducto,
    Lote,
    OperarioId,
    Ubicacion
FROM dbo.LotesNodrizas
WHERE FechaHora = CAST('1900-01-01 00:00:00' AS DATETIME2)
ORDER BY Id;

PRINT '';
-- =====================================================
-- ACTUALIZAR REGISTROS ESPECÍFICOS CON SUS FECHAS CORRESPONDIENTES
-- =====================================================
PRINT 'Actualizando registros específicos con sus fechas correspondientes...';

-- Actualizar registro ID 10312 con su fecha específica
UPDATE dbo.LotesNodrizas
SET FechaHora = CAST('2021-06-07 07:15:00' AS DATETIME2),
    FechaModificacion = GETDATE()
WHERE Id = 10312;

-- Actualizar registro ID 10620 con su fecha específica
UPDATE dbo.LotesNodrizas
SET FechaHora = CAST('2021-07-01 08:33:00' AS DATETIME2),
    FechaModificacion = GETDATE()
WHERE Id = 10620;

PRINT 'Registros específicos actualizados: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- Verificar las actualizaciones específicas
SELECT
    Id,
    FechaHora,
    NodrizaId,
    IdProducto,
    Lote,
    OperarioId,
    Ubicacion,
    FechaModificacion
FROM dbo.LotesNodrizas
WHERE Id IN (10312, 10620)
ORDER BY Id;

PRINT '✅ Migración completada exitosamente con FechaHora NOT NULL';
PRINT 'Los registros específicos tienen sus fechas correspondientes:'
PRINT '- ID 10312: 2021-06-07 07:15:00'
PRINT '- ID 10620: 2021-07-01 08:33:00';
