﻿namespace GestionAPQ_BLZ.Repositories.Dato01Lita;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class ALotessRepo : IALotessRepo
{
    private readonly Dato01LitaContext _dbContext;
    private readonly DbSet<ALotess> _dbSet;

    public ALotessRepo(Dato01LitaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<ALotess>();
    }

    public IQueryable<ALotess> GetIQueryableLotesConStock(string? idProducto, QueryOptions options)
    {
        var query = options.SetOptions(_dbSet);
        query = query.Where(i => i.Stock.HasValue
                                 && i.Stock.Value > 0);

        if (!string.IsNullOrEmpty(idProducto))
            query = query.Where(i => i.Codigo.Equals(idProducto));

        return query;
    }
}