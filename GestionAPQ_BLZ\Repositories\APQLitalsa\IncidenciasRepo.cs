﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class IncidenciasRepo : IIncidenciasRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Incidencias> _dbSet;

    public IncidenciasRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Incidencias>();
    }


    public async Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(
        string codigo,
        string lote, 
        string ubicacion,
        IncidenciasQueryOptions options, 
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);
        return await query
            .Where(i => i.Codigo.Equals(codigo)
                        && i.Lote.Equals(lote)
                        && i.Ubicacion.Equals(ubicacion))
            .FirstOrDefaultAsync(cancellationToken);
    }
}