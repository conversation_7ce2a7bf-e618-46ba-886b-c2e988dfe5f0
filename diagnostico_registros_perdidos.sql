-- <PERSON><PERSON>t para diagnosticar por qué se pierden registros en la migración
USE ApqLitalsa;

PRINT '=== DIAGNÓSTICO DE REGISTROS PERDIDOS ===';

-- 1. Contar registros totales en Access
DECLARE @TotalAccess INT;
SELECT @TotalAccess = COUNT(*)
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
);

PRINT 'Total de registros en Access: ' + CAST(@TotalAccess AS NVARCHAR(10));

-- 2. Contar registros migrados
DECLARE @TotalMigrados INT;
SELECT @TotalMigrados = COUNT(*) FROM dbo.LotesNodrizas;
PRINT 'Total de registros migrados: ' + CAST(@TotalMigrados AS NVARCHAR(10));
PRINT 'Registros perdidos: ' + CAST(@TotalAccess - @TotalMigrados AS NVARCHAR(10));

-- 3. <PERSON><PERSON>zar registros por condiciones
PRINT '';
PRINT '=== ANÁLISIS POR CONDICIONES ===';

-- Registros con NODRIZA NULL
SELECT 
    'NODRIZA IS NULL' as Condicion,
    COUNT(*) as Cantidad,
    CAST(COUNT(*) * 100.0 / @TotalAccess AS DECIMAL(5,2)) as Porcentaje
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA IS NULL

UNION ALL

-- Registros con NODRIZA = 0
SELECT 
    'NODRIZA = 0' as Condicion,
    COUNT(*) as Cantidad,
    CAST(COUNT(*) * 100.0 / @TotalAccess AS DECIMAL(5,2)) as Porcentaje
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA = 0

UNION ALL

-- Registros con OPERARIO NULL
SELECT 
    'OPERARIO IS NULL' as Condicion,
    COUNT(*) as Cantidad,
    CAST(COUNT(*) * 100.0 / @TotalAccess AS DECIMAL(5,2)) as Porcentaje
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.OPERARIO IS NULL

UNION ALL

-- Registros con OPERARIO vacío
SELECT 
    'OPERARIO VACÍO' as Condicion,
    COUNT(*) as Cantidad,
    CAST(COUNT(*) * 100.0 / @TotalAccess AS DECIMAL(5,2)) as Porcentaje
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.OPERARIO IS NOT NULL AND LTRIM(RTRIM(ln.OPERARIO)) = '';

-- 4. Verificar nodrizas que no existen en la tabla Nodrizas
PRINT '';
PRINT '=== NODRIZAS QUE NO EXISTEN EN TABLA NODRIZAS ===';
SELECT 
    ln.NODRIZA,
    COUNT(*) as CantidadRegistros
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE NODRIZA IS NOT NULL AND NODRIZA != 0'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
WHERE n.Id IS NULL
GROUP BY ln.NODRIZA
ORDER BY COUNT(*) DESC;

-- 5. Verificar operarios que no existen en la tabla Operarios
PRINT '';
PRINT '=== OPERARIOS QUE NO EXISTEN EN TABLA OPERARIOS ===';
SELECT TOP 20
    ln.OPERARIO,
    COUNT(*) as CantidadRegistros
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE OPERARIO IS NOT NULL'
) ln
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE op.Id IS NULL AND LTRIM(RTRIM(ln.OPERARIO)) != ''
GROUP BY ln.OPERARIO
ORDER BY COUNT(*) DESC;

-- 6. Simular la migración con condiciones actuales
PRINT '';
PRINT '=== SIMULACIÓN DE MIGRACIÓN ===';
SELECT 
    'Registros que se migrarían con condiciones actuales' as Descripcion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA IS NOT NULL; -- Solo condición actual

-- 7. Mostrar ejemplos de registros que se están perdiendo
PRINT '';
PRINT '=== EJEMPLOS DE REGISTROS QUE SE PIERDEN ===';
SELECT TOP 10
    ln.Id,
    ln.FECHA,
    ln.HORA,
    ln.NODRIZA,
    ln.OPERARIO,
    ln.IDPRODUCTO,
    ln.LOTE,
    CASE 
        WHEN ln.NODRIZA IS NULL THEN 'NODRIZA NULL'
        WHEN ln.NODRIZA = 0 THEN 'NODRIZA = 0'
        ELSE 'OTRO PROBLEMA'
    END as Problema
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA IS NULL
ORDER BY ln.Id;

PRINT '';
PRINT '=== RECOMENDACIONES ===';
PRINT '1. Si hay muchos registros con NODRIZA NULL, considerar migrarlos con NodrizaId = NULL';
PRINT '2. Verificar que todas las nodrizas referenciadas existan en la tabla Nodrizas';
PRINT '3. Los operarios inexistentes se asignarán automáticamente a DESCONOCIDO';
PRINT '4. Revisar la lógica de limpieza de nombres de operarios';

PRINT '';
PRINT 'Diagnóstico completado.';
