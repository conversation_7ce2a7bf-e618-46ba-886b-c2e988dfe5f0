-- <PERSON><PERSON>t para diagnosticar por qué se pierden registros en la migración
USE ApqLitalsa;

PRINT '=== DIAGNÓSTICO DE REGISTROS PERDIDOS ===';

-- 1. Contar registros totales en Access
DECLARE @TotalAccess INT;
SELECT @TotalAccess = COUNT(*)
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
);

PRINT 'Total de registros en Access: ' + CAST(@TotalAccess AS NVARCHAR(10));

-- 2. Contar registros migrados
DECLARE @TotalMigrados INT;
SELECT @TotalMigrados = COUNT(*) FROM dbo.LotesNodrizas;
PRINT 'Total de registros migrados: ' + CAST(@TotalMigrados AS NVARCHAR(10));
PRINT 'Registros perdidos: ' + CAST(@TotalAccess - @TotalMigrados AS NVARCHAR(10));

-- 3. <PERSON><PERSON><PERSON> registros por condiciones
PRINT '';
PRINT '=== ANÁLISIS POR CONDICIONES ===';

-- Registros con NODRIZA NULL
SELECT
    'NODRIZA IS NULL' as Condicion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA IS NULL

UNION ALL

-- Registros con NODRIZA = 0
SELECT
    'NODRIZA = 0' as Condicion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA = 0

UNION ALL

-- Registros con OPERARIO NULL
SELECT
    'OPERARIO IS NULL' as Condicion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.OPERARIO IS NULL;

-- 4. Verificar nodrizas que no existen en la tabla Nodrizas
PRINT '';
PRINT '=== NODRIZAS QUE NO EXISTEN EN TABLA NODRIZAS ===';
SELECT TOP 10
    ln.NODRIZA,
    COUNT(*) as CantidadRegistros
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE NODRIZA IS NOT NULL AND NODRIZA != 0'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
WHERE n.Id IS NULL
GROUP BY ln.NODRIZA
ORDER BY COUNT(*) DESC;

-- 5. Verificar operarios que no existen
PRINT '';
PRINT '=== OPERARIOS QUE NO EXISTEN EN TABLA OPERARIOS ===';
SELECT TOP 10
    ln.OPERARIO,
    COUNT(*) as CantidadRegistros
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE OPERARIO IS NOT NULL'
) ln
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE op.Id IS NULL AND LTRIM(RTRIM(ln.OPERARIO)) != ''
GROUP BY ln.OPERARIO
ORDER BY COUNT(*) DESC;

-- 6. Simular migración sin restricciones
PRINT '';
PRINT '=== SIMULACIÓN DE MIGRACIÓN SIN RESTRICCIONES ===';
SELECT
    'Registros que se migrarían SIN restricciones' as Descripcion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln;

-- 7. Simular migración solo excluyendo NODRIZA NULL
SELECT
    'Registros que se migrarían excluyendo solo NODRIZA NULL' as Descripcion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA IS NOT NULL;

PRINT '';
PRINT 'Diagnóstico completado. Ejecuta este script para identificar el problema.';
