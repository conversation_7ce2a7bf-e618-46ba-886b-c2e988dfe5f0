-- Script de prueba para migrar LotesNodrizas SIN WHERE
-- Esto nos permitirá ver exactamente qué errores aparecen
USE ApqLitalsa;

-- Limpiar tabla si existe
IF EXISTS (SELECT 1 FROM dbo.LotesNodrizas)
BEGIN
    TRUNCATE TABLE dbo.LotesNodrizas;
    PRINT 'Tabla LotesNodrizas limpiada.';
END

-- Crear operario DESCONOCIDO si no existe
IF NOT EXISTS (SELECT 1 FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
BEGIN
    INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
    VALUES ('DESCONOCIDO', NULL, NULL, 0);
    PRINT 'Operario DESCONOCIDO creado con ID: ' + CAST(SCOPE_IDENTITY() AS NVARCHAR(10));
END
ELSE
BEGIN
    DECLARE @DesconocidoId INT = (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO');
    PRINT 'Operario DESCONOCIDO ya existe con ID: ' + CAST(@DesconocidoId AS NVARCHAR(10));
END

PRINT 'Iniciando migración SIN WHERE...';

-- Migración SIN WHERE para ver todos los errores
INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT 
    -- Unificar fecha y hora
    CASE 
        WHEN ln.FECHA IS NOT NULL AND ln.HORA IS NOT NULL THEN 
            DATEADD(SECOND, 
                DATEPART(HOUR, ln.HORA) * 3600 + 
                DATEPART(MINUTE, ln.HORA) * 60 + 
                DATEPART(SECOND, ln.HORA), 
                CAST(ln.FECHA AS DATETIME2))
        WHEN ln.FECHA IS NOT NULL THEN 
            CAST(ln.FECHA AS DATETIME2)
        ELSE 
            CAST('1900-01-01 00:00:00' AS DATETIME2) -- Valor por defecto
    END as FechaHora,
    
    -- Buscar el ID de la nodriza (NULL si NODRIZA es NULL o 0)
    CASE 
        WHEN ln.NODRIZA IS NULL OR ln.NODRIZA = 0 THEN NULL 
        ELSE n.Id 
    END as NodrizaId,
    
    ln.IDPRODUCTO,
    ln.LOTE,
    
    -- Buscar el ID del operario (DESCONOCIDO si es NULL o no existe)
    ISNULL(
        CASE
            WHEN ln.OPERARIO IS NULL OR LTRIM(RTRIM(ln.OPERARIO)) = '' THEN NULL
            ELSE op.Id
        END,
        (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
    ) as OperarioId,
    
    ln.[UBICACIÓN]
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''));
-- ✅ SIN WHERE - MIGRAR TODO

PRINT 'Migración completada. Registros migrados: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- Verificar resultados
SELECT 
    'Total migrado' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas

UNION ALL

SELECT 
    'Con NodrizaId NULL' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas
WHERE NodrizaId IS NULL

UNION ALL

SELECT 
    'Con operario DESCONOCIDO' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas ln
INNER JOIN dbo.Operarios op ON ln.OperarioId = op.Id
WHERE op.Nombre = 'DESCONOCIDO';

PRINT 'Verificación completada. Si hay errores, aparecerán arriba.';
