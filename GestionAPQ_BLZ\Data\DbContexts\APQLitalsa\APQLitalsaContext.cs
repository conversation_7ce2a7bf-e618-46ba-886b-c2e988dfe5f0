﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

namespace GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;

public partial class APQLitalsaContext : DbContext
{
    public APQLitalsaContext(DbContextOptions<APQLitalsaContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Operarios> Operarios { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Operarios>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.HasIndex(e => e.Activo, "IX_Operarios_Activo");

            entity.Property(e => e.Activo).HasDefaultValue(true);
            entity.Property(e => e.Apellido1).HasMaxLength(50);
            entity.Property(e => e.Apellido2).HasMaxLength(50);
            entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FechaModificacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(50);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}