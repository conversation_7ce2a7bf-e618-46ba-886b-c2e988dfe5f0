﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

namespace GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;

public partial class APQLitalsaContext : DbContext
{
    public APQLitalsaContext(DbContextOptions<APQLitalsaContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Incidencias> Incidencias { get; set; }

    public virtual DbSet<LotesNodrizas> LotesNodrizas { get; set; }

    public virtual DbSet<Nodrizas> Nodrizas { get; set; }

    public virtual DbSet<Operarios> Operarios { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Incidencias>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.HasIndex(e => e.Codigo, "IX_Incidencias_Codigo");

            entity.HasIndex(e => e.<PERSON>d<PERSON><PERSON><PERSON>, "IX_Incidencias_IdOperario");

            entity.HasIndex(e => e.Lote, "IX_Incidencias_Lote");

            entity.HasIndex(e => e.Ubicacion, "IX_Incidencias_Ubicacion");

            entity.Property(e => e.Codigo).HasMaxLength(50);
            entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FechaModificacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Lote).HasMaxLength(50);
            entity.Property(e => e.Ubicacion).HasMaxLength(100);

            entity.HasOne(d => d.IdOperarioNavigation).WithMany(p => p.Incidencias)
                .HasForeignKey(d => d.IdOperario)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Incidencias_Operario");
        });

        modelBuilder.Entity<Incidencias>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Incidenc__3214EC073366EA67");

            entity.HasIndex(e => e.Codigo, "IX_Incidencias_Codigo");

            entity.HasIndex(e => e.IdOperario, "IX_Incidencias_IdOperario");

            entity.HasIndex(e => e.Lote, "IX_Incidencias_Lote");

            entity.HasIndex(e => e.Ubicacion, "IX_Incidencias_Ubicacion");

            entity.HasIndex(e => new { e.Codigo, e.Lote, e.Ubicacion }, "UK_Incidencias_Codigo_Lote_Ubicacion").IsUnique();

            entity.Property(e => e.Codigo)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FechaModificacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Incidencia).IsRequired();
            entity.Property(e => e.Lote)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Ubicacion)
                .IsRequired()
                .HasMaxLength(100);

            entity.HasOne(d => d.IdOperarioNavigation).WithMany(p => p.Incidencias)
                .HasForeignKey(d => d.IdOperario)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Incidencias_Operario");
        });

        modelBuilder.Entity<LotesNodrizas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__LotesNod__3214EC0714BBFDA4");

            entity.HasIndex(e => e.IdNodriza, "IX_LotesNodrizas_IdNodriza");

            entity.HasIndex(e => e.IdOperario, "IX_LotesNodrizas_IdOperario");

            entity.HasIndex(e => e.IdProducto, "IX_LotesNodrizas_IdProducto");

            entity.HasIndex(e => e.Lote, "IX_LotesNodrizas_Lote");

            entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FechaModificacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Lote).HasMaxLength(50);
            entity.Property(e => e.Ubicacion).HasMaxLength(100);

            entity.HasOne(d => d.IdNodrizaNavigation).WithMany(p => p.LotesNodrizas)
                .HasForeignKey(d => d.IdNodriza)
                .HasConstraintName("FK_LotesNodrizas_Nodriza");

            entity.HasOne(d => d.IdOperarioNavigation).WithMany(p => p.LotesNodrizas)
                .HasForeignKey(d => d.IdOperario)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_LotesNodrizas_Operario");
        });

        modelBuilder.Entity<Nodrizas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Nodrizas__3214EC07622B83AE");

            entity.HasIndex(e => e.Activo, "IX_Nodrizas_Activo");

            entity.HasIndex(e => e.Borrada, "IX_Nodrizas_Borrada");

            entity.HasIndex(e => e.IdProducto, "IX_Nodrizas_IdProducto");

            entity.HasIndex(e => e.NumNodriza, "IX_Nodrizas_NumNodriza");

            entity.Property(e => e.Activo).HasDefaultValue(true);
            entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FechaModificacion).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Operarios>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Operario__3214EC0785732CDD");

            entity.HasIndex(e => e.Activo, "IX_Operarios_Activo");

            entity.Property(e => e.Activo).HasDefaultValue(true);
            entity.Property(e => e.Apellido1).HasMaxLength(50);
            entity.Property(e => e.Apellido2).HasMaxLength(50);
            entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FechaModificacion).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(50);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}