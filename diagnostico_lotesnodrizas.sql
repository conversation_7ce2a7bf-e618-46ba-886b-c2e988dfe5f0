-- Script de diagnóstico para identificar problemas en LotesNodrizas
USE ApqLitalsa;

PRINT '=== DIAGNÓSTICO DE DATOS EN LotesNodrizas ===';

-- 1. Contar registros totales
SELECT 
    'Total de registros en LotesNodrizas' as Descripcion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
);

-- 2. Analizar problemas de datos
SELECT 
    'Registros con FECHA NULL' as Problema,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.FECHA IS NULL

UNION ALL

SELECT 
    'Registros con NODRIZA NULL' as Problema,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.NODRIZA IS NULL

UNION ALL

SELECT 
    'Registros con OPERARIO NULL o vacío' as Problema,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
WHERE ln.OPERARIO IS NULL OR LTRIM(RTRIM(ln.OPERARIO)) = '';

-- 3. Mostrar ejemplos de registros problemáticos con contexto para interpolación
PRINT '=== EJEMPLOS DE REGISTROS CON FECHA NULL Y SU CONTEXTO ===';
SELECT
    ln.Id,
    ln.FECHA,
    ln.HORA,
    ln.NODRIZA,
    ln.OPERARIO,
    -- Fecha anterior
    LAG(ln.FECHA, 1) OVER (ORDER BY ln.Id) as FechaAnterior,
    -- Fecha posterior
    LEAD(ln.FECHA, 1) OVER (ORDER BY ln.Id) as FechaPosterior,
    -- Fecha que se asignaría
    CASE
        WHEN ln.FECHA IS NOT NULL THEN ln.FECHA
        WHEN LAG(ln.FECHA, 1) OVER (ORDER BY ln.Id) IS NOT NULL THEN
            DATEADD(DAY, 1, LAG(ln.FECHA, 1) OVER (ORDER BY ln.Id))
        WHEN LEAD(ln.FECHA, 1) OVER (ORDER BY ln.Id) IS NOT NULL THEN
            DATEADD(DAY, -1, LEAD(ln.FECHA, 1) OVER (ORDER BY ln.Id))
        ELSE
            CAST('2024-01-01' AS DATETIME)
    END as FechaCalculada
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] ORDER BY Id'
) ln
WHERE ln.Id IN (
    SELECT TOP 10 Id
    FROM OPENROWSET(
        'Microsoft.ACE.OLEDB.12.0',
        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
        'SELECT * FROM [LotesNodrizas] WHERE FECHA IS NULL ORDER BY Id'
    )
)
ORDER BY ln.Id;

-- 4. Verificar nodrizas que no existen en la tabla Nodrizas
PRINT '=== NODRIZAS QUE NO EXISTEN EN LA TABLA NODRIZAS ===';
SELECT DISTINCT 
    ln.NODRIZA,
    COUNT(*) as CantidadRegistros
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE NODRIZA IS NOT NULL'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
WHERE n.Id IS NULL
GROUP BY ln.NODRIZA
ORDER BY ln.NODRIZA;

-- 5. Verificar operarios que no existen en la tabla Operarios
PRINT '=== OPERARIOS QUE NO EXISTEN EN LA TABLA OPERARIOS ===';
SELECT DISTINCT 
    ln.OPERARIO,
    COUNT(*) as CantidadRegistros
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE OPERARIO IS NOT NULL'
) ln
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE op.Id IS NULL AND LTRIM(RTRIM(ln.OPERARIO)) != ''
GROUP BY ln.OPERARIO
ORDER BY COUNT(*) DESC;

-- 6. Contar registros que se pueden migrar exitosamente
SELECT 
    'Registros que se pueden migrar' as Descripcion,
    COUNT(*) as Cantidad
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE ln.NODRIZA IS NOT NULL 
  AND ln.OPERARIO IS NOT NULL 
  AND LTRIM(RTRIM(ln.OPERARIO)) != ''
  AND n.Id IS NOT NULL 
  AND op.Id IS NOT NULL;

-- 7. Script de migración corregido (solo registros válidos)
PRINT '=== SCRIPT DE MIGRACIÓN SUGERIDO ===';
PRINT 'INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)';
PRINT 'SELECT';
PRINT '    CASE';
PRINT '        WHEN ln.FECHA IS NOT NULL AND ln.HORA IS NOT NULL THEN';
PRINT '            DATEADD(SECOND,';
PRINT '                DATEPART(HOUR, ln.HORA) * 3600 +';
PRINT '                DATEPART(MINUTE, ln.HORA) * 60 +';
PRINT '                DATEPART(SECOND, ln.HORA),';
PRINT '                CAST(ln.FECHA AS DATETIME2))';
PRINT '        WHEN ln.FECHA IS NOT NULL THEN';
PRINT '            CAST(ln.FECHA AS DATETIME2)';
PRINT '        ELSE';
PRINT '            CAST(''1900-01-01 00:00:00'' AS DATETIME2)';
PRINT '    END as FechaHora,';
PRINT '    n.Id as NodrizaId,';
PRINT '    ln.IDPRODUCTO,';
PRINT '    ln.LOTE,';
PRINT '    op.Id as OperarioId,';
PRINT '    ln.[UBICACIÓN]';
PRINT 'FROM OPENROWSET(...) ln';
PRINT 'LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza';
PRINT 'LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL('' '' + op.Apellido1, '''') + ISNULL('' '' + op.Apellido2, ''''))';
PRINT 'WHERE ln.NODRIZA IS NOT NULL';
PRINT '  AND ln.OPERARIO IS NOT NULL';
PRINT '  AND LTRIM(RTRIM(ln.OPERARIO)) != ''''';
PRINT '  AND n.Id IS NOT NULL';
PRINT '  AND op.Id IS NOT NULL;';

PRINT '=== FIN DEL DIAGNÓSTICO ===';
