# Mejoras en la Tabla LotesNodrizas

## Resumen de Cambios

Se ha mejorado significativamente la tabla `LotesNodrizas` implementando relaciones apropiadas con foreign keys y unificando los campos de fecha y hora.

## Estructura Anterior vs Nueva

### LotesNodrizas (Anterior)
```sql
CREATE TABLE LOTESNODRIZAS (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FECHA DATETIME,
    HORA TIME,
    NODRIZA INT,                    -- Número de nodriza
    IDPRODUCTO INT,
    LOTE NVARCHAR(50),
    OPERARIO NVARCHAR(50),          -- Nombre del operario
    UBICACION NVARCHAR(100)
);
```

### LotesNodrizas (Nueva)
```sql
CREATE TABLE dbo.LotesNodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FechaHora DATETIME2 NOT NULL,           -- ✅ NUEVO: Unifica Fecha + Hora
    NodrizaId INT NOT NULL,                 -- ✅ NUEVO: FK a Nodrizas
    IdProducto INT NOT NULL,                -- Mantenido
    Lote NVARCHAR(50),                      -- Mantenido
    OperarioId INT NOT NULL,                -- ✅ NUEVO: FK a Operarios
    Ubicacion NVARCHAR(100),                -- Mantenido
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),
    
    -- Foreign Keys
    CONSTRAINT FK_LotesNodrizas_Nodriza FOREIGN KEY (NodrizaId) 
        REFERENCES dbo.Nodrizas(Id),
    CONSTRAINT FK_LotesNodrizas_Operario FOREIGN KEY (OperarioId) 
        REFERENCES dbo.Operarios(Id)
);
```

## Mejoras Implementadas

### 1. **Campo FechaHora Unificado**
- **Antes**: Dos campos separados `FECHA` (DATE) y `HORA` (TIME)
- **Ahora**: Un solo campo `FechaHora` (DATETIME2)
- **Beneficio**: Más eficiente para consultas y ordenamiento

### 2. **Foreign Key a Nodrizas**
- **Antes**: `NODRIZA INT` (número de nodriza)
- **Ahora**: `NodrizaId INT` con FK a `dbo.Nodrizas(Id)`
- **Beneficio**: Integridad referencial y mejor rendimiento en JOINs

### 3. **Foreign Key a Operarios**
- **Antes**: `OPERARIO NVARCHAR(50)` (nombre del operario)
- **Ahora**: `OperarioId INT` con FK a `dbo.Operarios(Id)`
- **Beneficio**: Consistencia de datos y mejor rendimiento

### 4. **Campos de Auditoría**
- **Nuevo**: `FechaCreacion DATETIME2`
- **Nuevo**: `FechaModificacion DATETIME2`
- **Beneficio**: Trazabilidad de cambios

### 5. **Índices Optimizados**
```sql
INDEX IX_LotesNodrizas_FechaHora (FechaHora),
INDEX IX_LotesNodrizas_NodrizaId (NodrizaId),
INDEX IX_LotesNodrizas_IdProducto (IdProducto),
INDEX IX_LotesNodrizas_OperarioId (OperarioId),
INDEX IX_LotesNodrizas_Lote (Lote)
```

## Migración de Datos

```sql
INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT 
    -- Unificar fecha y hora en un solo campo
    CASE 
        WHEN ln.HORA IS NOT NULL THEN 
            CAST(CAST(ln.FECHA AS DATE) AS DATETIME2) + CAST(ln.HORA AS DATETIME2)
        ELSE 
            CAST(ln.FECHA AS DATETIME2)
    END as FechaHora,
    
    -- Buscar el ID de la nodriza por su número
    n.Id as NodrizaId,
    
    ln.IDPRODUCTO,
    ln.LOTE,
    
    -- Buscar el ID del operario por su nombre
    op.Id as OperarioId,
    
    ln.UBICACION
FROM OPENROWSET(...) ln
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE ln.NODRIZA IS NOT NULL 
  AND ln.OPERARIO IS NOT NULL 
  AND n.Id IS NOT NULL 
  AND op.Id IS NOT NULL;
```

## Vista de Compatibilidad

Para mantener la compatibilidad con el código existente:

```sql
CREATE VIEW vw_LotesNodrizas_Compatible AS
SELECT
    ln.Id,
    CAST(ln.FechaHora AS DATE) AS FECHA,
    CAST(ln.FechaHora AS TIME) AS HORA,
    n.NumNodriza AS NODRIZA,
    ln.IdProducto AS IDPRODUCTO,
    ln.Lote AS LOTE,
    (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, '')) AS OPERARIO,
    ln.Ubicacion AS UBICACION
FROM dbo.LotesNodrizas ln
LEFT JOIN dbo.Nodrizas n ON ln.NodrizaId = n.Id
LEFT JOIN dbo.Operarios op ON ln.OperarioId = op.Id;
```

## Archivos Creados

### 1. **DTO**
- `GestionAPQ_BLZ/DTO/Genericos/LotesNodrizasDTO.cs`

### 2. **Entidad**
- `GestionAPQ_BLZ/Data/Entities/APQLitalsa/LotesNodrizas.cs`

## Beneficios de la Nueva Estructura

### ✅ **Mejor Integridad de Datos**
- Foreign keys evitan datos huérfanos
- No se pueden insertar lotes con operarios o nodrizas inexistentes

### ✅ **Mejor Rendimiento**
- JOINs más eficientes con claves numéricas
- Índices optimizados para consultas frecuentes
- Campo fecha unificado mejora ordenamiento

### ✅ **Mejor Mantenibilidad**
- Cambios en nombres de operarios se reflejan automáticamente
- Estructura más clara y consistente

### ✅ **Mejor Funcionalidad**
- Consultas más simples con propiedades de navegación
- Auditoría completa de cambios

## Ejemplo de Uso

### Consulta con la Nueva Estructura
```csharp
// Obtener lotes con información de nodriza y operario
var lotes = await context.LotesNodrizas
    .Include(l => l.Nodriza)
    .Include(l => l.Operario)
    .Where(l => l.FechaHora >= DateTime.Today.AddDays(-30))
    .OrderByDescending(l => l.FechaHora)
    .ToListAsync();

// Crear nuevo lote
var nuevoLote = new LotesNodrizas
{
    FechaHora = DateTime.Now,
    NodrizaId = 5,
    IdProducto = 123,
    Lote = "L2024001",
    OperarioId = 3,
    Ubicacion = "A1-B2"
};
```

### Consulta de Compatibilidad
```sql
-- Usar la vista para código existente
SELECT * FROM vw_LotesNodrizas_Compatible
WHERE FECHA >= '2024-01-01'
ORDER BY FECHA DESC, HORA DESC;
```

## Próximos Pasos

1. **Actualizar Entity Framework** para incluir las nuevas entidades
2. **Crear repositorios** específicos para LotesNodrizas
3. **Actualizar código de la aplicación** para usar las nuevas FK
4. **Aprovechar las propiedades de navegación** para simplificar consultas
5. **Migrar gradualmente** del uso de vistas a la nueva estructura

---

Esta nueva estructura proporciona una base sólida y eficiente para la gestión de lotes de nodrizas con integridad referencial completa.
