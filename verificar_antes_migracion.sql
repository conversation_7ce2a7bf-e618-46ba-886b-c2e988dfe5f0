-- Script para verificar que todo está listo antes de la migración
USE ApqLitalsa;

PRINT '=== VERIFICACIÓN PRE-MIGRACIÓN ===';

-- 1. Verificar que existe el operario DESCONOCIDO
DECLARE @DesconocidoId INT = (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO');

IF @DesconocidoId IS NULL
BEGIN
    PRINT '❌ ERROR: No existe el operario DESCONOCIDO';
    PRINT 'Ejecuta primero: INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo) VALUES (''DESCONOCIDO'', NULL, NULL, 0);';
END
ELSE
BEGIN
    PRINT '✅ Operario DESCONOCIDO existe con ID: ' + CAST(@DesconocidoId AS NVARCHAR(10));
END

-- 2. Verificar estructura de la tabla LotesNodrizas
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'LotesNodrizas' AND COLUMN_NAME = 'NodrizaId' AND IS_NULLABLE = 'YES')
BEGIN
    PRINT '❌ ERROR: La columna NodrizaId debe permitir NULL';
    PRINT 'Ejecuta: ALTER TABLE dbo.LotesNodrizas ALTER COLUMN NodrizaId INT NULL;';
END
ELSE
BEGIN
    PRINT '✅ Columna NodrizaId permite NULL correctamente';
END

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'LotesNodrizas' AND COLUMN_NAME = 'OperarioId' AND IS_NULLABLE = 'NO')
BEGIN
    PRINT '❌ ERROR: La columna OperarioId debe ser NOT NULL';
    PRINT 'Ejecuta: ALTER TABLE dbo.LotesNodrizas ALTER COLUMN OperarioId INT NOT NULL;';
END
ELSE
BEGIN
    PRINT '✅ Columna OperarioId es NOT NULL correctamente';
END

-- 3. Contar registros en Access
DECLARE @TotalAccess INT;
SELECT @TotalAccess = COUNT(*)
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas]'
);

PRINT '📊 Total de registros en Access: ' + CAST(@TotalAccess AS NVARCHAR(10));

-- 4. Verificar que la función LimpiarTexto existe
IF OBJECT_ID('dbo.LimpiarTexto', 'FN') IS NULL
BEGIN
    PRINT '❌ ERROR: No existe la función dbo.LimpiarTexto';
    PRINT 'Ejecuta primero la parte del script que crea las funciones';
END
ELSE
BEGIN
    PRINT '✅ Función dbo.LimpiarTexto existe';
END

-- 5. Contar operarios y nodrizas disponibles
DECLARE @TotalOperarios INT = (SELECT COUNT(*) FROM dbo.Operarios);
DECLARE @TotalNodrizas INT = (SELECT COUNT(*) FROM dbo.Nodrizas);

PRINT '📊 Total de operarios disponibles: ' + CAST(@TotalOperarios AS NVARCHAR(10));
PRINT '📊 Total de nodrizas disponibles: ' + CAST(@TotalNodrizas AS NVARCHAR(10));

-- 6. Test de la lógica de asignación de operario
DECLARE @TestOperarioId INT;
SET @TestOperarioId = ISNULL(
    CASE 
        WHEN 'OPERARIO_INEXISTENTE' IS NULL OR LTRIM(RTRIM('OPERARIO_INEXISTENTE')) = '' THEN NULL
        ELSE (SELECT Id FROM dbo.Operarios WHERE Nombre = 'OPERARIO_INEXISTENTE')
    END,
    (SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO')
);

IF @TestOperarioId IS NULL
BEGIN
    PRINT '❌ ERROR: La lógica de asignación de operario DESCONOCIDO falla';
END
ELSE
BEGIN
    PRINT '✅ Lógica de asignación de operario DESCONOCIDO funciona correctamente';
END

PRINT '';
PRINT '=== RESUMEN ===';
IF @DesconocidoId IS NOT NULL 
   AND OBJECT_ID('dbo.LimpiarTexto', 'FN') IS NOT NULL
   AND @TestOperarioId IS NOT NULL
BEGIN
    PRINT '🟢 TODO LISTO PARA LA MIGRACIÓN';
    PRINT 'Puedes ejecutar el script de migración sin WHERE';
END
ELSE
BEGIN
    PRINT '🔴 HAY PROBLEMAS QUE CORREGIR ANTES DE LA MIGRACIÓN';
    PRINT 'Revisa los errores marcados con ❌ arriba';
END
