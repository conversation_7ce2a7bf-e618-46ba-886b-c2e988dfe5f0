﻿namespace GestionAPQ_BLZ.Repositories.Base.DatoLita01;

using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;

public interface IAArticuRepo : IRepository<AArticu>
{
    public IQueryable<AArticu> GetIQueryableBarnices(string? idBarniz, bool asNoTracking);

    public Task<List<AArticu>> GetBarnices(string? idBarniz, bool asNoTracking,
        CancellationToken cancellationToken);

    public Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidades(string? idBarniz,
        IAViscosRepo aViscosRepo,
        bool asNoTracking,
        CancellationToken cancellationToken);
}