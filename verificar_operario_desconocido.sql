-- Script para verificar los registros asignados al operario DESCONOCIDO
USE ApqLitalsa;

PRINT '=== VERIFICACIÓN DEL OPERARIO DESCONOCIDO ===';

-- 1. Verificar que existe el operario DESCONOCIDO
SELECT 
    'Operario DESCONOCIDO' as Verificacion,
    CASE 
        WHEN EXISTS (SELECT 1 FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO') 
        THEN 'EXISTE' 
        ELSE 'NO EXISTE' 
    END as Estado,
    ISNULL((SELECT Id FROM dbo.Operarios WHERE Nombre = 'DESCONOCIDO'), 0) as Id;

-- 2. Contar registros asignados al operario DESCONOCIDO
SELECT 
    'Registros con operario DESCONOCIDO' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas ln
INNER JOIN dbo.Operarios op ON ln.OperarioId = op.Id
WHERE op.Nombre = 'DESCONOCIDO';

-- 3. Mostrar algunos ejemplos de registros con operario DESCONOCIDO
PRINT '=== EJEMPLOS DE REGISTROS CON OPERARIO DESCONOCIDO ===';
SELECT TOP 10
    ln.Id,
    ln.FechaHora,
    CASE 
        WHEN ln.NodrizaId IS NULL THEN 'SIN NODRIZA'
        ELSE CAST(n.NumNodriza AS NVARCHAR(10))
    END as Nodriza,
    ln.IdProducto,
    ln.Lote,
    op.Nombre as Operario,
    ln.Ubicacion
FROM dbo.LotesNodrizas ln
INNER JOIN dbo.Operarios op ON ln.OperarioId = op.Id
LEFT JOIN dbo.Nodrizas n ON ln.NodrizaId = n.Id
WHERE op.Nombre = 'DESCONOCIDO'
ORDER BY ln.FechaHora DESC;

-- 4. Estadísticas generales de operarios
PRINT '=== ESTADÍSTICAS DE OPERARIOS EN LOTESNODRIZAS ===';
SELECT 
    op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, '') as NombreCompleto,
    op.Activo,
    COUNT(*) as CantidadLotes
FROM dbo.LotesNodrizas ln
INNER JOIN dbo.Operarios op ON ln.OperarioId = op.Id
GROUP BY op.Id, op.Nombre, op.Apellido1, op.Apellido2, op.Activo
ORDER BY COUNT(*) DESC;

-- 5. Verificar integridad referencial
PRINT '=== VERIFICACIÓN DE INTEGRIDAD REFERENCIAL ===';
SELECT 
    'Lotes sin operario válido' as Problema,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas ln
LEFT JOIN dbo.Operarios op ON ln.OperarioId = op.Id
WHERE op.Id IS NULL

UNION ALL

SELECT 
    'Lotes con nodriza inválida' as Problema,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas ln
LEFT JOIN dbo.Nodrizas n ON ln.NodrizaId = n.Id
WHERE ln.NodrizaId IS NOT NULL AND n.Id IS NULL;

-- 6. Resumen final
PRINT '=== RESUMEN FINAL ===';
SELECT 
    'Total de lotes migrados' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas

UNION ALL

SELECT 
    'Lotes con operario DESCONOCIDO' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas ln
INNER JOIN dbo.Operarios op ON ln.OperarioId = op.Id
WHERE op.Nombre = 'DESCONOCIDO'

UNION ALL

SELECT 
    'Lotes sin nodriza (NodrizaId NULL)' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas
WHERE NodrizaId IS NULL

UNION ALL

SELECT 
    'Lotes con nodriza válida' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas
WHERE NodrizaId IS NOT NULL;

PRINT 'Verificación completada.';
