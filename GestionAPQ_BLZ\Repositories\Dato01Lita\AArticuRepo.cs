﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AArticuRepo : Repository<AArticu, Dato01LitaContext>, IAArticuRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AArticuRepo(Dato01LitaContext dbContext) : base(dbContext)
    {
    }

    public IQueryable<AArticu> GetIQueryableBarnices(string? idBarniz, bool asNoTracking)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        query = query.Where(i => i.Estadis.StartsWith("BABA"));
        query = !string.IsNullOrEmpty(idBarniz) ? query.Where(i => i.Codigo.Equals(idBarniz)) : query;

        return query;
    }

    public async Task<List<AArticu>> GetBarnices(string? idBarniz, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        var listaBarnices = await GetIQueryableBarnices(idBarniz, asNoTracking).ToListAsync();
        return listaBarnices;
    }

    public async Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidades(string? idBarniz,
        IAViscosRepo aViscosRepo,
        bool asNoTracking,
        CancellationToken cancellationToken)
    {
        var queryBarnices = GetIQueryableBarnices(idBarniz, asNoTracking);
        var queryViscosidades = aViscosRepo.GetIQueryableViscosidades(idBarniz, asNoTracking);

        var query = from barniz in queryBarnices
            join viscos in queryViscosidades on barniz.Codigo equals viscos.Codigo into viscosGroup
            from viscos in viscosGroup.DefaultIfEmpty()
            select new ValueTuple<AArticu, AViscos>(barniz, viscos);

        return await query.ToListAsync(cancellationToken);
    }
}