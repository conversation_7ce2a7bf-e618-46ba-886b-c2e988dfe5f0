﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AViscosRepo : Repository<AViscos, Dato01LitaContext>, IAViscosRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AViscosRepo(Dato01LitaContext dbContext) : base(dbContext)
    {
    }

    public IQueryable<AViscos> GetIQueryableViscosidades(string? codigo, bool asNoTracking)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        query = !string.IsNullOrEmpty(codigo) ? query.Where(i => i.Codigo.Equals(codigo)) : query;

        return query;
    }
}