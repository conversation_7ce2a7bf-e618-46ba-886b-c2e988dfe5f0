# Migración de TablaProductosNodrizas a Nodrizas

## Resumen de Cambios

Se ha transformado la tabla `TablaProductosNodrizas` en una nueva tabla llamada `Nodrizas` con una estructura más clara y funcional.

## Estructura Anterior vs Nueva

### TablaProductosNodrizas (Anterior)
```sql
CREATE TABLE TablaProductosNodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    IdNodriza INT,
    Idproducto INT,
    Obsoleto BIT,
    FechaRetirada DATETIME,
    Activo BIT
);
```

### Nodrizas (Nueva)
```sql
CREATE TABLE dbo.Nodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    NumNodriza INT NOT NULL,
    IdProducto INT NOT NULL,
    Activo BIT NOT NULL DEFAULT 1,
    Borrada BIT NOT NULL DEFAULT 0,
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),
    
    -- Índices para mejorar rendimiento
    INDEX IX_Nodrizas_NumNodriza (NumNodriza),
    INDEX IX_Nodrizas_IdProducto (IdProducto),
    INDEX IX_Nodrizas_Activo (Activo),
    INDEX IX_Nodrizas_Borrada (Borrada),
    
    -- Constraint único para evitar duplicados
    CONSTRAINT UK_Nodrizas_NumNodriza_IdProducto UNIQUE (NumNodriza, IdProducto)
);
```

## Mejoras Implementadas

### 1. **Nombres de Campos Más Claros**
- `IdNodriza` → `NumNodriza` (más descriptivo)
- `Idproducto` → `IdProducto` (capitalización consistente)

### 2. **Campos Obligatorios**
- `NumNodriza` y `IdProducto` ahora son `NOT NULL`
- Evita datos inconsistentes

### 3. **Campo Borrada**
- Nuevo campo `Borrada BIT NOT NULL DEFAULT 0`
- Permite borrado lógico en lugar de físico
- Todas las nodrizas migradas quedan con `Borrada = 0`

### 4. **Campos de Auditoría**
- `FechaCreacion DATETIME2 DEFAULT GETDATE()`
- `FechaModificacion DATETIME2 DEFAULT GETDATE()`
- Mejor trazabilidad de cambios

### 5. **Índices de Rendimiento**
- Índice en `NumNodriza` para búsquedas rápidas
- Índice en `IdProducto` para filtros por producto
- Índice en `Activo` para filtrar nodrizas activas
- Índice en `Borrada` para excluir borradas

### 6. **Constraint de Unicidad**
- `UNIQUE (NumNodriza, IdProducto)`
- Evita duplicados de la misma nodriza con el mismo producto

### 7. **Eliminación de Campos Obsoletos**
- Se eliminó `Obsoleto` (redundante con `Activo`)
- Se eliminó `FechaRetirada` (se puede usar `FechaModificacion` + `Activo = 0`)

## Migración de Datos

```sql
INSERT INTO dbo.Nodrizas (NumNodriza, IdProducto, Activo, Borrada)
SELECT 
    IdNodriza,
    Idproducto,
    Activo,
    0 -- Todas las nodrizas se migran como no borradas
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaProductosNodrizas] WHERE IdNodriza IS NOT NULL AND Idproducto IS NOT NULL'
)
WHERE IdNodriza IS NOT NULL AND Idproducto IS NOT NULL;
```

## Vista de Compatibilidad

Para mantener la compatibilidad con el código existente, se ha creado una vista:

```sql
CREATE VIEW vw_TablaProductosNodrizas_Compatible AS
SELECT
    Id,
    NumNodriza AS IdNodriza,
    IdProducto AS Idproducto,
    CAST(0 AS BIT) AS Obsoleto,
    NULL AS FechaRetirada,
    Activo
FROM dbo.Nodrizas
WHERE Borrada = 0;
```

## Archivos Creados

### 1. **DTO**
- `GestionAPQ_BLZ/DTO/Genericos/NodrizaDTO.cs`

### 2. **Entidad**
- `GestionAPQ_BLZ/Data/Entities/APQLitalsa/Nodrizas.cs`

### 3. **Scripts**
- `actualizar_dbcontext.sql` - Guía para actualizar Entity Framework

## Próximos Pasos

### 1. **Actualizar Entity Framework**
1. Editar `efpt.config.json` para incluir la tabla `Nodrizas`
2. Regenerar el DbContext usando EF Core Power Tools
3. Verificar que la entidad se generó correctamente

### 2. **Crear Repositorio**
1. Crear interfaz `INodrizasRepo`
2. Crear implementación `NodrizasRepo`
3. Registrar en `DependencyInjection.cs`

### 3. **Actualizar Código de la Aplicación**
1. Cambiar referencias de `TablaProductosNodrizasDTO` a `NodrizaDTO`
2. Actualizar consultas para usar la nueva estructura
3. Aprovechar los nuevos campos (`Borrada`, fechas de auditoría)

### 4. **Testing**
1. Verificar que la migración fue exitosa
2. Probar funcionalidades existentes
3. Validar que no hay regresiones

## Beneficios de la Nueva Estructura

### ✅ **Mejor Rendimiento**
- Índices optimizados para consultas frecuentes
- Constraint de unicidad evita duplicados

### ✅ **Mejor Integridad de Datos**
- Campos obligatorios evitan datos nulos
- Constraint único evita inconsistencias

### ✅ **Mejor Trazabilidad**
- Campos de auditoría para tracking de cambios
- Borrado lógico mantiene historial

### ✅ **Mejor Mantenibilidad**
- Nombres de campos más descriptivos
- Estructura más clara y consistente

### ✅ **Compatibilidad**
- Vista de compatibilidad mantiene código existente funcionando
- Migración gradual posible

## Ejemplo de Uso

```csharp
// Crear nueva nodriza
var nuevaNodriza = new NodrizaDTO
{
    NumNodriza = 5,
    IdProducto = 123,
    Activo = true,
    Borrada = false
};

// Buscar nodrizas activas
var nodrizasActivas = await mediator.Send(
    new GetAllEntitiesQuery<Nodrizas, NodrizaDTO>(
        filter: n => n.Activo && !n.Borrada
    )
);

// Borrado lógico
nodriza.Borrada = true;
nodriza.FechaModificacion = DateTime.Now;
await mediator.Send(new GrabarNodrizaCommand(nodriza));
```

---

Esta nueva estructura proporciona una base sólida y escalable para la gestión de nodrizas en la aplicación.
