namespace GestionAPQ_BLZ.Services;

using Base;
using Microsoft.JSInterop;

public class DocumentService : IDocumentService
{
    private readonly IWebHostEnvironment _environment;
    private readonly IJSRuntime _jsRuntime;

    public DocumentService(IJSRuntime jsRuntime, IWebHostEnvironment environment)
    {
        _jsRuntime = jsRuntime;
        _environment = environment;
    }

    public string? GetRutaDocumento(string tipoDoc, string? strAuxiliar = null)
    {
        var (directory, pattern) = tipoDoc switch
        {
            "FichaTecnica" => (@"\\*************\in2$\datos\dato01LITA\DOCS\ARTICULOS\",
                $"{strAuxiliar}*00-00-00*.pdf"),
            "IT1" => (@"\\DC1\Sistema de Gestion 2015\3 INSTRUCCIONES\INSTRUCCIONES CONTROL DE CALIDAD", "IQ-15*"),
            "IT2" => (@"\\DC1\Sistema de Gestion 2015\3 INSTRUCCIONES\INSTRUCCIONES CONTROL DE CALIDAD", "IQ-02*"),
            _ => (null, null)
        };

        return Directory.GetFiles(directory, pattern).FirstOrDefault();
    }

    public async Task<bool> AbrirDocumento(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return false;

            var fileName = Path.GetFileName(filePath);
            var tempPath = Path.Combine(_environment.WebRootPath, "temp-pdfs");

            // Crear directorio temporal si no existe
            if (!Directory.Exists(tempPath))
                Directory.CreateDirectory(tempPath);

            // Limpiar archivos anteriores
            foreach (var file in Directory.GetFiles(tempPath))
                File.Delete(file);

            // Copiar archivo a la carpeta temporal
            var destinationPath = Path.Combine(tempPath, fileName);
            File.Copy(filePath, destinationPath, true);

            // Abrir en nueva pestaña
            var url = $"/temp-pdfs/{fileName}";
            await _jsRuntime.InvokeVoidAsync("window.open", url, "_blank");

            return true;
        }
        catch
        {
            return false;
        }
    }
}