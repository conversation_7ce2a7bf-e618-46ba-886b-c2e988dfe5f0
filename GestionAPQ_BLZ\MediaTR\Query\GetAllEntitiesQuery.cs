namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;

public record GetAllEntitiesQuery<TEntity, TDto>(QueryOptions QueryOptions)
    : IRequest<ListResult<TDto>> where TEntity : class;

public static class GetAllEntities
{
    public static GetAllEntitiesQuery<Nodrizas, NodrizaDTO> ForNodrizas(NodrizasQueryOptions options)
    {
        return new GetAllEntitiesQuery<Nodrizas, NodrizaDTO>(options);
    }

    public static GetAllEntitiesQuery<Operarios, OperarioDTO> ForOperarios(OperariosQueryOptions options)
    {
        return new GetAllEntitiesQuery<Operarios, OperarioDTO>(options);
    }
}

public class
    GetAllEntitiesQueryHandler<TEntity, TDto> : IRequestHandler<GetAllEntitiesQuery<TEntity, TDto>, ListResult<TDto>>
    where TEntity : class
{
    private readonly IServiceProvider _serviceProvider;

    public GetAllEntitiesQueryHandler(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<ListResult<TDto>> Handle(GetAllEntitiesQuery<TEntity, TDto> request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<TDto>
        {
            Data = [],
            Errors = []
        };

        try
        {
            // Usar las QueryOptions pasadas o crear unas por defecto
            var options = request.QueryOptions;

            // Switch según el tipo de entidad - mantener tipos específicos
            if (typeof(TEntity) == typeof(Nodrizas))
            {
                var entities = await GetNodrizas(options, cancellationToken);
                result.Data = TinyMapper.Map<List<TDto>>(entities);
            }
            else if (typeof(TEntity) == typeof(Operarios))
            {
                var entities = await GetOperarios(options, cancellationToken);
                result.Data = TinyMapper.Map<List<TDto>>(entities);
            }
        }
        catch (Exception ex)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private async Task<List<Nodrizas>> GetNodrizas(QueryOptions options, CancellationToken cancellationToken)
    {
        var repo = _serviceProvider.GetRequiredService<INodrizasRepo>();
        var nodrizasOptions = options as NodrizasQueryOptions;

        return await repo.GetAll(nodrizasOptions, cancellationToken);
    }

    private async Task<List<Operarios>> GetOperarios(QueryOptions options, CancellationToken cancellationToken)
    {
        var repo = _serviceProvider.GetRequiredService<IOperariosRepo>();
        var operariosOptions = options as OperariosQueryOptions;

        return await repo.GetAll(operariosOptions, cancellationToken);
    }
}