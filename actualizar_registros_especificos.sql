-- Script para actualizar registros específicos con FechaHora NULL
-- Basado en la imagen proporcionada
USE ApqLitalsa;

PRINT '=== ACTUALIZANDO REGISTROS ESPECÍFICOS CON FECHAHORA NULL ===';

-- Mostrar registros antes de la actualización
PRINT 'Registros ANTES de la actualización:';
SELECT 
    Id, 
    FechaHora, 
    NodrizaId, 
    IdProducto, 
    Lote, 
    OperarioId, 
    Ubicacion
FROM dbo.LotesNodrizas 
WHERE Id IN (10312, 10620)
ORDER BY Id;

-- Actualizar registro ID 10312 con su fecha específica
UPDATE dbo.LotesNodrizas 
SET FechaHora = CAST('2021-06-07 07:15:00' AS DATETIME2),
    FechaModificacion = GETDATE()
WHERE Id = 10312 AND FechaHora IS NULL;

PRINT 'Registro 10312 actualizado: ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' filas afectadas';

-- Actualizar registro ID 10620 con su fecha específica  
UPDATE dbo.LotesNodrizas 
SET FechaHora = CAST('2021-07-01 08:33:00' AS DATETIME2),
    FechaModificacion = GETDATE()
WHERE Id = 10620 AND FechaHora IS NULL;

PRINT 'Registro 10620 actualizado: ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' filas afectadas';

-- Mostrar registros después de la actualización
PRINT '';
PRINT 'Registros DESPUÉS de la actualización:';
SELECT 
    Id, 
    FechaHora, 
    NodrizaId, 
    IdProducto, 
    Lote, 
    OperarioId, 
    Ubicacion,
    FechaModificacion
FROM dbo.LotesNodrizas 
WHERE Id IN (10312, 10620)
ORDER BY Id;

-- Verificar que ya no hay registros con FechaHora NULL
SELECT 
    'Registros con FechaHora NULL después de actualización' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas 
WHERE FechaHora IS NULL;

PRINT '';
PRINT '✅ Actualización completada exitosamente';
PRINT 'Fechas asignadas:';
PRINT '- ID 10312: 2021-06-07 07:15:00';
PRINT '- ID 10620: 2021-07-01 08:33:00';
PRINT '';
PRINT 'Ahora puedes cambiar la columna FechaHora a NOT NULL:';
PRINT 'ALTER TABLE dbo.LotesNodrizas ALTER COLUMN FechaHora DATETIME2 NOT NULL;';
