-- Script alternativo para actualizar registros con FechaHora NULL usando fecha más reciente
USE ApqLitalsa;

PRINT '=== ACTUALIZANDO REGISTROS CON FECHAHORA NULL (FECHA RECIENTE) ===';

-- Opción 1: Usar fecha actual
-- UPDATE dbo.LotesNodrizas 
-- SET FechaHora = GETDATE(),
--     FechaModificacion = GETDATE()
-- WHERE FechaHora IS NULL;

-- Opción 2: Usar una fecha específica más reciente (ej: 2024-01-01)
UPDATE dbo.LotesNodrizas 
SET FechaHora = CAST('2024-01-01 00:00:00' AS DATETIME2),
    FechaModificacion = GETDATE()
WHERE FechaHora IS NULL;

PRINT 'Registros actualizados con fecha 2024-01-01: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- Opción 3: Usar la fecha del registro más reciente como referencia
-- DECLARE @FechaReferencia DATETIME2;
-- SELECT @FechaReferencia = MAX(FechaHora) FROM dbo.LotesNodrizas WHERE FechaHora IS NOT NULL;
-- 
-- UPDATE dbo.LotesNodrizas 
-- SET FechaHora = @FechaReferencia,
--     FechaModificacion = GETDATE()
-- WHERE FechaHora IS NULL;

-- Verificar resultado
SELECT 
    'Registros con FechaHora NULL después de actualización' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas 
WHERE FechaHora IS NULL;

PRINT 'Actualización completada.';
