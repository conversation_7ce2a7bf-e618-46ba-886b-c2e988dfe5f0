-- Script para migrar LotesNodrizas con interpolación de fechas NULL
USE ApqLitalsa;

-- =====================================================
-- PASO 1: CREAR TABLA TEMPORAL CON DATOS DE ACCESS
-- =====================================================
PRINT 'Creando tabla temporal con datos de Access...';

-- Crear tabla temporal
CREATE TABLE #LotesTemp (
    Id INT,
    FECHA DATETIME,
    HORA TIME,
    NODRIZA INT,
    IDPRODUCTO INT,
    LOTE NVARCHAR(50),
    OPERARIO NVARCHAR(255),
    UBICACION NVARCHAR(100)
);

-- Llenar tabla temporal con datos de Access
INSERT INTO #LotesTemp (Id, FECHA, HORA, NODRIZA, IDPRODUCTO, LOTE, OPERARIO, UBICACION)
SELECT 
    Id, FECHA, HORA, NODRIZA, IDPRODUCTO, LOTE, OPERARIO, [UBICACIÓN]
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] ORDER BY Id'
);

PRINT 'Datos cargados en tabla temporal. Total de registros: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =====================================================
-- PASO 2: CALCULAR FECHAS INTERPOLADAS
-- =====================================================
PRINT 'Calculando fechas interpoladas...';

-- Añadir columna para fecha calculada
ALTER TABLE #LotesTemp ADD FechaCalculada DATETIME;

-- Actualizar fechas calculadas usando un cursor para mayor control
DECLARE @Id INT, @Fecha DATETIME, @FechaAnterior DATETIME, @FechaPosterior DATETIME;
DECLARE @FechaCalculada DATETIME;

DECLARE cursor_fechas CURSOR FOR
SELECT Id, FECHA FROM #LotesTemp ORDER BY Id;

OPEN cursor_fechas;
FETCH NEXT FROM cursor_fechas INTO @Id, @Fecha;

WHILE @@FETCH_STATUS = 0
BEGIN
    IF @Fecha IS NOT NULL
    BEGIN
        -- Si tiene fecha, usar esa fecha
        SET @FechaCalculada = @Fecha;
    END
    ELSE
    BEGIN
        -- Si no tiene fecha, buscar fecha anterior
        SELECT TOP 1 @FechaAnterior = FECHA 
        FROM #LotesTemp 
        WHERE Id < @Id AND FECHA IS NOT NULL 
        ORDER BY Id DESC;
        
        -- Buscar fecha posterior
        SELECT TOP 1 @FechaPosterior = FECHA 
        FROM #LotesTemp 
        WHERE Id > @Id AND FECHA IS NOT NULL 
        ORDER BY Id ASC;
        
        -- Calcular fecha interpolada
        IF @FechaAnterior IS NOT NULL
        BEGIN
            -- Usar día siguiente al anterior
            SET @FechaCalculada = DATEADD(DAY, 1, @FechaAnterior);
        END
        ELSE IF @FechaPosterior IS NOT NULL
        BEGIN
            -- Usar día anterior al posterior
            SET @FechaCalculada = DATEADD(DAY, -1, @FechaPosterior);
        END
        ELSE
        BEGIN
            -- Valor por defecto
            SET @FechaCalculada = CAST('2024-01-01' AS DATETIME);
        END
    END
    
    -- Actualizar la fecha calculada
    UPDATE #LotesTemp 
    SET FechaCalculada = @FechaCalculada 
    WHERE Id = @Id;
    
    FETCH NEXT FROM cursor_fechas INTO @Id, @Fecha;
END

CLOSE cursor_fechas;
DEALLOCATE cursor_fechas;

-- =====================================================
-- PASO 3: MOSTRAR ESTADÍSTICAS DE INTERPOLACIÓN
-- =====================================================
PRINT 'Estadísticas de interpolación:';

SELECT 
    'Registros con fecha original' as Tipo,
    COUNT(*) as Cantidad
FROM #LotesTemp 
WHERE FECHA IS NOT NULL

UNION ALL

SELECT 
    'Registros con fecha interpolada' as Tipo,
    COUNT(*) as Cantidad
FROM #LotesTemp 
WHERE FECHA IS NULL AND FechaCalculada IS NOT NULL;

-- Mostrar algunos ejemplos de interpolación
PRINT 'Ejemplos de fechas interpoladas:';
SELECT TOP 10
    Id,
    FECHA as FechaOriginal,
    FechaCalculada,
    CASE 
        WHEN FECHA IS NULL THEN 'INTERPOLADA'
        ELSE 'ORIGINAL'
    END as Tipo
FROM #LotesTemp
WHERE FECHA IS NULL
ORDER BY Id;

-- =====================================================
-- PASO 4: MIGRAR A LA TABLA FINAL
-- =====================================================
PRINT 'Migrando datos a LotesNodrizas...';

INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT 
    -- Unificar fecha calculada y hora
    CASE 
        WHEN lt.HORA IS NOT NULL THEN 
            DATEADD(SECOND, 
                DATEPART(HOUR, lt.HORA) * 3600 + 
                DATEPART(MINUTE, lt.HORA) * 60 + 
                DATEPART(SECOND, lt.HORA), 
                CAST(lt.FechaCalculada AS DATETIME2))
        ELSE 
            CAST(lt.FechaCalculada AS DATETIME2)
    END as FechaHora,
    
    n.Id as NodrizaId,
    lt.IDPRODUCTO,
    lt.LOTE,
    op.Id as OperarioId,
    lt.UBICACION
FROM #LotesTemp lt
LEFT JOIN dbo.Nodrizas n ON lt.NODRIZA = n.NumNodriza
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(lt.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE lt.NODRIZA IS NOT NULL 
  AND lt.OPERARIO IS NOT NULL 
  AND LTRIM(RTRIM(lt.OPERARIO)) != ''
  AND n.Id IS NOT NULL 
  AND op.Id IS NOT NULL
  AND lt.FechaCalculada IS NOT NULL;

PRINT 'Migración completada. Registros migrados: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =====================================================
-- PASO 5: LIMPIAR Y VERIFICAR
-- =====================================================

-- Eliminar tabla temporal
DROP TABLE #LotesTemp;

-- Verificar resultados
SELECT 
    'LotesNodrizas migrados' as Tabla,
    COUNT(*) as TotalRegistros,
    MIN(FechaHora) as FechaMasAntigua,
    MAX(FechaHora) as FechaMasReciente
FROM dbo.LotesNodrizas;

-- Mostrar algunos ejemplos
SELECT TOP 10
    ln.Id,
    ln.FechaHora,
    n.NumNodriza,
    ln.IdProducto,
    ln.Lote,
    (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, '')) as Operario,
    ln.Ubicacion
FROM dbo.LotesNodrizas ln
LEFT JOIN dbo.Nodrizas n ON ln.NodrizaId = n.Id
LEFT JOIN dbo.Operarios op ON ln.OperarioId = op.Id
ORDER BY ln.FechaHora DESC;

PRINT 'Script de migración con interpolación completado exitosamente.';
