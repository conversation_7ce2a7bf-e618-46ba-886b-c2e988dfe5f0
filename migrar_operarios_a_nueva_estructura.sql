-- Script para migrar la tabla de operarios actual a la nueva estructura con nombre y apellidos
-- Ejecutar DESPUÉS de haber ejecutado el script de limpieza de operarios duplicados

USE ApqLitalsa;

-- =====================================================
-- PASO 1: CREAR FUNCIONES DE SEPARACIÓN DE NOMBRES
-- =====================================================

-- Función para separar nombre completo en partes
IF OBJECT_ID('dbo.SepararNombreCompleto', 'TF') IS NULL
BEGIN
    EXEC('
    CREATE FUNCTION dbo.SepararNombreCompleto(@nombreCompleto NVARCHAR(255))
    RETURNS TABLE
    AS
    RETURN (
        SELECT 
            CASE 
                -- Casos especiales conocidos
                WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE ''<PERSON><PERSON><PERSON>'' THEN ''Álvaro''
                WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE ''Jesus Carra'' THEN ''Jesús''
                -- Caso general: primera palabra es el nombre
                ELSE 
                    CASE 
                        WHEN CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) > 0 
                        THEN LEFT(dbo.LimpiarTexto(@nombreCompleto), CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) - 1)
                        ELSE dbo.LimpiarTexto(@nombreCompleto)
                    END
            END AS Nombre,
            
            CASE 
                -- Casos especiales conocidos
                WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE ''Álvaro Gonzalez'' THEN ''González''
                WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE ''Jesus Carra'' THEN ''Carra''
                -- Caso general: segunda palabra es el primer apellido
                ELSE CASE 
                        WHEN CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) > 0 
                        THEN 
                            CASE 
                                WHEN CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) + 1) > 0 
                                THEN SUBSTRING(
                                        dbo.LimpiarTexto(@nombreCompleto), 
                                        CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) + 1,
                                        CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) + 1) - CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) - 1
                                     )
                                ELSE SUBSTRING(
                                        dbo.LimpiarTexto(@nombreCompleto), 
                                        CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) + 1,
                                        LEN(dbo.LimpiarTexto(@nombreCompleto))
                                     )
                            END
                        ELSE NULL
                    END
            END AS Apellido1,
            
            CASE 
                -- Casos especiales conocidos
                WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE ''Álvaro Gonzalez'' THEN NULL
                WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE ''Jesus Carra'' THEN NULL
                -- Caso general: tercera palabra en adelante es el segundo apellido
                ELSE CASE 
                        WHEN CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) + 1) > 0 
                        THEN SUBSTRING(
                                dbo.LimpiarTexto(@nombreCompleto), 
                                CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX('' '', dbo.LimpiarTexto(@nombreCompleto)) + 1) + 1,
                                LEN(dbo.LimpiarTexto(@nombreCompleto))
                             )
                        ELSE NULL
                    END
            END AS Apellido2
    );
    ');
END;

-- =====================================================
-- PASO 2: CREAR TABLA TEMPORAL PARA LA MIGRACIÓN
-- =====================================================

-- Crear tabla temporal para la migración
CREATE TABLE #OperariosMigrados (
    Id INT,
    Nombre NVARCHAR(50),
    Apellido1 NVARCHAR(50),
    Apellido2 NVARCHAR(50),
    Activo BIT,
    FechaCreacion DATETIME2,
    FechaModificacion DATETIME2
);

-- Llenar tabla temporal con datos separados
INSERT INTO #OperariosMigrados (Id, Nombre, Apellido1, Apellido2, Activo, FechaCreacion, FechaModificacion)
SELECT 
    o.Id,
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    o.Activo,
    o.FechaCreacion,
    GETDATE() as FechaModificacion
FROM TablaOperarios o
CROSS APPLY dbo.SepararNombreCompleto(o.Operario) sep;

-- Mostrar resultados de la separación
SELECT 
    o.Id,
    o.Operario as OperarioOriginal,
    m.Nombre,
    m.Apellido1,
    m.Apellido2,
    o.Activo
FROM TablaOperarios o
INNER JOIN #OperariosMigrados m ON o.Id = m.Id
ORDER BY o.Id;

-- =====================================================
-- PASO 3: CREAR NUEVA TABLA DE OPERARIOS
-- =====================================================

-- Crear nueva tabla con la estructura deseada
CREATE TABLE dbo.Operarios (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Nombre NVARCHAR(50) NOT NULL,
    Apellido1 NVARCHAR(50),
    Apellido2 NVARCHAR(50),
    NombreCompleto AS (Nombre + ISNULL(' ' + Apellido1, '') + ISNULL(' ' + Apellido2, '')) PERSISTED,
    Activo BIT NOT NULL DEFAULT 1,
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),
    
    -- Índice en el campo calculado para búsquedas
    INDEX IX_Operarios_NombreCompleto (NombreCompleto)
);

-- =====================================================
-- PASO 4: MIGRAR DATOS A LA NUEVA TABLA
-- =====================================================

-- Desactivar IDENTITY_INSERT para permitir insertar IDs específicos
SET IDENTITY_INSERT dbo.Operarios ON;

-- Insertar datos en la nueva tabla
INSERT INTO dbo.Operarios (Id, Nombre, Apellido1, Apellido2, Activo, FechaCreacion, FechaModificacion)
SELECT 
    Id,
    Nombre,
    Apellido1,
    Apellido2,
    Activo,
    FechaCreacion,
    FechaModificacion
FROM #OperariosMigrados;

-- Reactivar IDENTITY_INSERT
SET IDENTITY_INSERT dbo.Operarios OFF;

-- =====================================================
-- PASO 5: ACTUALIZAR REFERENCIAS EN OTRAS TABLAS
-- =====================================================

-- Actualizar referencias en LOTESNODRIZAS
ALTER TABLE dbo.LOTESNODRIZAS
DROP CONSTRAINT FK_LotesNodrizas_Operario;

ALTER TABLE dbo.LOTESNODRIZAS
ADD CONSTRAINT FK_LotesNodrizas_Operario FOREIGN KEY (OperarioId) 
REFERENCES dbo.Operarios(Id);

-- Actualizar referencias en Incidencias
ALTER TABLE dbo.Incidencias
DROP CONSTRAINT FK_Incidencias_CreadoPor;

ALTER TABLE dbo.Incidencias
ADD CONSTRAINT FK_Incidencias_CreadoPor FOREIGN KEY (CreadoPorId) 
REFERENCES dbo.Operarios(Id);

-- Actualizar referencias en TablaInspecciones
ALTER TABLE dbo.TablaInspecciones
DROP CONSTRAINT FK_TablaInspecciones_RealizadoPor;

ALTER TABLE dbo.TablaInspecciones
ADD CONSTRAINT FK_TablaInspecciones_RealizadoPor FOREIGN KEY (RealizadoPorId) 
REFERENCES dbo.Operarios(Id);

-- Actualizar referencias en TablaPesosEnvases
ALTER TABLE dbo.TablaPesosEnvases
DROP CONSTRAINT FK_TablaPesosEnvases_RealizadoPor;

ALTER TABLE dbo.TablaPesosEnvases
ADD CONSTRAINT FK_TablaPesosEnvases_RealizadoPor FOREIGN KEY (RealizadoPorId) 
REFERENCES dbo.Operarios(Id);

-- =====================================================
-- PASO 6: ACTUALIZAR VISTAS DE COMPATIBILIDAD
-- =====================================================

-- Actualizar vista de LOTESNODRIZAS
ALTER VIEW vw_LOTESNODRIZAS_Compatible AS
SELECT 
    ln.Id,
    ln.FECHA,
    ln.HORA,
    ln.NODRIZA,
    ln.IDPRODUCTO,
    ln.LOTE,
    op.NombreCompleto as OPERARIO,
    ln.Ubicacion,
    ln.FechaCreacion
FROM dbo.LOTESNODRIZAS ln
LEFT JOIN dbo.Operarios op ON ln.OperarioId = op.Id;

-- Actualizar vista de Incidencias
ALTER VIEW vw_Incidencias_Compatible AS
SELECT 
    inc.Id,
    inc.Codigo,
    inc.Lote,
    inc.FechaCreacion,
    op.NombreCompleto as CreadoPor,
    inc.Incidencia,
    inc.Ubicacion,
    inc.Estado,
    inc.FechaResolucion
FROM dbo.Incidencias inc
LEFT JOIN dbo.Operarios op ON inc.CreadoPorId = op.Id;

-- Actualizar vista de TablaInspecciones
ALTER VIEW vw_TablaInspecciones_Compatible AS
SELECT 
    ti.IdInspecciones,
    ti.Idproducto,
    ti.Idlote,
    ti.Viscosidad,
    ti.Solidos,
    ti.ObservacionesAplicacion,
    ti.Inspeccion,
    op.NombreCompleto as RealizadoPor,
    ti.Fecha,
    ti.TemperaturaViscosidad,
    ti.ObservacionesQ
FROM dbo.TablaInspecciones ti
LEFT JOIN dbo.Operarios op ON ti.RealizadoPorId = op.Id;

-- Actualizar vista de TablaPesosEnvases
ALTER VIEW vw_TablaPesosEnvases_Compatible AS
SELECT 
    tpe.Id,
    tpe.NUMENVASE,
    tpe.Idproducto,
    tpe.Fecha,
    tpe.FechaCaducidad,
    tpe.PESOTEORICOENTRADA,
    tpe.PESOREALENTRADA,
    tpe.DIFERENCIA,
    tpe.PESOVACIO,
    tpe.PESOENVASE,
    op.NombreCompleto as RealizadoPor,
    tpe.FechaFin,
    tpe.Ubicacion,
    tpe.Lote,
    tpe.StockActual,
    tpe.Observaciones
FROM dbo.TablaPesosEnvases tpe
LEFT JOIN dbo.Operarios op ON tpe.RealizadoPorId = op.Id;

-- =====================================================
-- PASO 7: LIMPIAR Y FINALIZAR
-- =====================================================

-- Eliminar tabla temporal
DROP TABLE #OperariosMigrados;

-- Renombrar tabla antigua (por seguridad, no eliminarla inmediatamente)
EXEC sp_rename 'TablaOperarios', 'TablaOperarios_OLD';

-- Renombrar nueva tabla
EXEC sp_rename 'Operarios', 'TablaOperarios';

PRINT 'Migración de operarios a la nueva estructura completada.';
PRINT 'La tabla antigua se ha renombrado a TablaOperarios_OLD.';
PRINT 'Puedes eliminarla cuando estés seguro de que todo funciona correctamente.';
