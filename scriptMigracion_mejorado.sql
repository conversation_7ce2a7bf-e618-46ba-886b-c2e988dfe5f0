-- Script de migración mejorado de Access a SQL Server
-- Incluye relaciones entre tablas y mejoras estructurales
USE ApqLitalsa;

-- =====================================================
-- PASO 1: CREAR TABLAS
-- =====================================================

-- Primero eliminamos las tablas existentes si existen (en orden por dependencias)
IF OBJECT_ID('dbo.LotesNodrizas', 'U') IS NOT NULL DROP TABLE dbo.LotesNodrizas;
IF OBJECT_ID('dbo.Nodrizas', 'U') IS NOT NULL DROP TABLE dbo.Nodrizas;
IF OBJECT_ID('dbo.Operarios', 'U') IS NOT NULL DROP TABLE dbo.Operarios;

-- =====================================================
-- TABLA MAESTRA: Operarios
-- =====================================================
CREATE TABLE dbo.Operarios (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Nombre NVARCHAR(50) NOT NULL,
    Apellido1 NVARCHAR(50),
    Apellido2 NVARCHAR(50),
    Activo BIT NOT NULL DEFAULT 1, -- Campo añadido para activo/inactivo
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE()
);

-- =====================================================
-- TABLA: Nodrizas (antes TablaProductosNodrizas)
-- =====================================================
CREATE TABLE dbo.Nodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    NumNodriza INT NOT NULL,
    IdProducto INT NOT NULL,
    Activo BIT NOT NULL DEFAULT 1,
    Borrada BIT NOT NULL DEFAULT 0, -- Todas quedarán a 0 inicialmente
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),

    -- Índices para mejorar rendimiento
    INDEX IX_Nodrizas_NumNodriza (NumNodriza),
    INDEX IX_Nodrizas_IdProducto (IdProducto),
    INDEX IX_Nodrizas_Activo (Activo),
    INDEX IX_Nodrizas_Borrada (Borrada),

    -- Constraint único para evitar duplicados
    CONSTRAINT UK_Nodrizas_NumNodriza_IdProducto UNIQUE (NumNodriza, IdProducto)
);

-- =====================================================
-- TABLA: LotesNodrizas (mejorada con FK y fecha unificada)
-- =====================================================
CREATE TABLE dbo.LotesNodrizas (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FechaHora DATETIME2 NOT NULL,           -- ✅ NUEVO: Unifica Fecha + Hora
    NodrizaId INT NOT NULL,                 -- ✅ NUEVO: FK a Nodrizas (era NODRIZA número)
    IdProducto INT NOT NULL,                -- Mantenido
    Lote NVARCHAR(50),                      -- Mantenido
    OperarioId INT NOT NULL,                -- ✅ NUEVO: FK a Operarios (era OPERARIO string)
    Ubicacion NVARCHAR(100),                -- Mantenido
    FechaCreacion DATETIME2 DEFAULT GETDATE(),
    FechaModificacion DATETIME2 DEFAULT GETDATE(),

    -- Foreign Keys
    CONSTRAINT FK_LotesNodrizas_Nodriza FOREIGN KEY (NodrizaId)
        REFERENCES dbo.Nodrizas(Id),
    CONSTRAINT FK_LotesNodrizas_Operario FOREIGN KEY (OperarioId)
        REFERENCES dbo.Operarios(Id),

    -- Índices para mejorar rendimiento
    INDEX IX_LotesNodrizas_FechaHora (FechaHora),
    INDEX IX_LotesNodrizas_NodrizaId (NodrizaId),
    INDEX IX_LotesNodrizas_IdProducto (IdProducto),
    INDEX IX_LotesNodrizas_OperarioId (OperarioId),
    INDEX IX_LotesNodrizas_Lote (Lote)
);

-- =====================================================
-- PASO 3: SP's PARA NORMALIZAR TEXTOS
-- =====================================================
-- Función para limpiar texto (eliminar saltos de línea, espacios múltiples, etc.)
-- CREATE OR ALTER FUNCTION dbo.LimpiarTexto(@texto NVARCHAR(255))
-- RETURNS NVARCHAR(255)
-- AS
-- BEGIN
    -- DECLARE @resultado NVARCHAR(255);

    -- -- Si es NULL o vacío, devolver NULL
    -- IF @texto IS NULL OR LTRIM(RTRIM(@texto)) = ''
        -- RETURN NULL;

    -- -- Limpiar el texto
    -- SET @resultado = @texto;

    -- -- Eliminar saltos de línea, tabulaciones y caracteres de control
    -- SET @resultado = REPLACE(@resultado, CHAR(13), ''); -- Carriage Return
    -- SET @resultado = REPLACE(@resultado, CHAR(10), ''); -- Line Feed
    -- SET @resultado = REPLACE(@resultado, CHAR(9), '');  -- Tab
    -- SET @resultado = REPLACE(@resultado, CHAR(160), ' '); -- Non-breaking space

    -- -- Eliminar espacios múltiples y trim
    -- WHILE CHARINDEX('  ', @resultado) > 0
        -- SET @resultado = REPLACE(@resultado, '  ', ' ');

    -- SET @resultado = LTRIM(RTRIM(@resultado));

    -- -- Si después de limpiar queda vacío, devolver NULL
    -- IF @resultado = ''
        -- RETURN NULL;

    -- RETURN @resultado;
-- END;

-- -- Función para normalizar nombres específicos
-- CREATE OR ALTER FUNCTION dbo.NormalizarNombre(@nombre NVARCHAR(50))
-- RETURNS NVARCHAR(50)
-- AS
-- BEGIN
    -- -- Normalizar casos específicos conocidos
    -- RETURN CASE
        -- WHEN @nombre LIKE '%Raul%' OR @nombre LIKE '%Raúl%' THEN 'Raúl'
        -- WHEN @nombre LIKE '%Kike%' THEN 'Kike'
        -- WHEN @nombre LIKE '%Javier%' THEN 'Javier'
        -- WHEN @nombre LIKE '%Miguel%' OR @nombre LIKE '%Migurl%' THEN 'Miguel'
        -- WHEN @nombre LIKE '%Alejandro%' THEN 'Alejandro'
        -- WHEN @nombre LIKE '%Alvaro%' OR @nombre LIKE '%Álvaro%' THEN 'Álvaro'
        -- WHEN @nombre LIKE '%Mateo%' THEN 'Mateo'
        -- WHEN @nombre LIKE '%Jesus%' OR @nombre LIKE '%Jesús%' THEN 'Jesús'
        -- WHEN @nombre LIKE '%Oscar%' OR @nombre LIKE '%Óscar%' THEN 'Óscar'
        -- WHEN @nombre LIKE '%Andres%' OR @nombre LIKE '%Andrés%' THEN 'Andrés'
        -- ELSE @nombre
    -- END;
-- END;

-- -- Función para separar nombre completo en partes
-- CREATE OR ALTER FUNCTION dbo.SepararNombreCompleto(@nombreCompleto NVARCHAR(255))
-- RETURNS TABLE
-- AS
-- RETURN (
    -- SELECT
        -- CASE
            -- -- Casos especiales conocidos
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Álvaro Gonzalez' THEN 'Álvaro'
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Jesus Carra' THEN 'Jesús'
            -- -- Caso general: primera palabra es el nombre
            -- ELSE dbo.NormalizarNombre(
                    -- CASE
                        -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) > 0
                        -- THEN LEFT(dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) - 1)
                        -- ELSE dbo.LimpiarTexto(@nombreCompleto)
                    -- END
                 -- )
        -- END AS Nombre,

        -- CASE
            -- -- Casos especiales conocidos
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Álvaro Gonzalez' THEN 'González'
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Jesus Carra' THEN 'Carra'
            -- -- Caso general: segunda palabra es el primer apellido
            -- ELSE CASE
                    -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) > 0
                    -- THEN
                        -- CASE
                            -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) > 0
                            -- THEN SUBSTRING(
                                    -- dbo.LimpiarTexto(@nombreCompleto),
                                    -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1,
                                    -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) - CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) - 1
                                 -- )
                            -- ELSE SUBSTRING(
                                    -- dbo.LimpiarTexto(@nombreCompleto),
                                    -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1,
                                    -- LEN(dbo.LimpiarTexto(@nombreCompleto))
                                 -- )
                        -- END
                    -- ELSE NULL
                -- END
        -- END AS Apellido1,

        -- CASE
            -- -- Casos especiales conocidos
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Álvaro Gonzalez' THEN NULL
            -- WHEN dbo.LimpiarTexto(@nombreCompleto) LIKE 'Jesus Carra' THEN NULL
            -- -- Caso general: tercera palabra en adelante es el segundo apellido
            -- ELSE CASE
                    -- WHEN CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) > 0
                    -- THEN SUBSTRING(
                            -- dbo.LimpiarTexto(@nombreCompleto),
                            -- CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto), CHARINDEX(' ', dbo.LimpiarTexto(@nombreCompleto)) + 1) + 1,
                            -- LEN(dbo.LimpiarTexto(@nombreCompleto))
                         -- )
                    -- ELSE NULL
                -- END
        -- END AS Apellido2
-- );

-- =====================================================
-- PASO 2: MIGRACIÓN DE DATOS
-- =====================================================
-- Migrar operarios primero (tabla maestra)
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    1 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaOperarios]'
) op
CROSS APPLY dbo.SepararNombreCompleto(op.Operario) sep
WHERE dbo.LimpiarTexto(Operario) IS NOT NULL;

-- Añadir operarios que aparecen en otras tablas pero no están en Operarios
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    0 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT OPERARIO FROM [LotesNodrizas] WHERE OPERARIO IS NOT NULL'
) ln
CROSS APPLY dbo.SepararNombreCompleto(ln.OPERARIO) sep
WHERE dbo.LimpiarTexto(OPERARIO) IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM dbo.Operarios
      WHERE Nombre = sep.Nombre
        AND ISNULL(Apellido1,'') = ISNULL(sep.Apellido1,'')
  );

-- Añadir operarios de RealizadoPor en TablaInspecciones
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    0 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT RealizadoPor FROM [TablaInspecciones] WHERE RealizadoPor IS NOT NULL'
) ti
CROSS APPLY dbo.SepararNombreCompleto(ti.RealizadoPor) sep
WHERE dbo.LimpiarTexto(RealizadoPor) IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM dbo.Operarios
      WHERE Nombre = sep.Nombre
        AND ISNULL(Apellido1,'') = ISNULL(sep.Apellido1,'')
  );

-- Añadir operarios de RealizadoPor en TablaPesosEnvases
INSERT INTO dbo.Operarios (Nombre, Apellido1, Apellido2, Activo)
SELECT DISTINCT
    sep.Nombre,
    sep.Apellido1,
    sep.Apellido2,
    0 as Activo
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT DISTINCT RealizadoPor FROM [TablaPesosEnvases] WHERE RealizadoPor IS NOT NULL'
) tpe
CROSS APPLY dbo.SepararNombreCompleto(tpe.RealizadoPor) sep
WHERE dbo.LimpiarTexto(RealizadoPor) IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM dbo.Operarios
      WHERE Nombre = sep.Nombre
        AND ISNULL(Apellido1,'') = ISNULL(sep.Apellido1,'')
  );



-- =====================================================
-- MIGRAR DATOS DE TablaProductosNodrizas A Nodrizas
-- =====================================================
INSERT INTO dbo.Nodrizas (NumNodriza, IdProducto, Activo, Borrada)
SELECT
    IdNodriza,
    Idproducto,
    Activo,
    0 -- Todas las nodrizas se migran como no borradas
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [TablaProductosNodrizas] WHERE IdNodriza IS NOT NULL AND Idproducto IS NOT NULL'
)
WHERE IdNodriza IS NOT NULL AND Idproducto IS NOT NULL;

-- =====================================================
-- MIGRAR DATOS DE LotesNodrizas (con FK y fecha unificada)
-- =====================================================
INSERT INTO dbo.LotesNodrizas (FechaHora, NodrizaId, IdProducto, Lote, OperarioId, Ubicacion)
SELECT
    -- Unificar fecha y hora en un solo campo
    CASE
        WHEN ln.HORA IS NOT NULL THEN
            CAST(CAST(ln.FECHA AS DATE) AS DATETIME2) + CAST(ln.HORA AS DATETIME2)
        ELSE
            CAST(ln.FECHA AS DATETIME2)
    END as FechaHora,

    -- Buscar el ID de la nodriza por su número
    n.Id as NodrizaId,

    ln.IDPRODUCTO,
    ln.LOTE,

    -- Buscar el ID del operario por su nombre (usando campo calculado NombreCompleto)
    op.Id as OperarioId,

    ln.UBICACION
FROM OPENROWSET(
    'Microsoft.ACE.OLEDB.12.0',
    'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',
    'SELECT * FROM [LotesNodrizas] WHERE NODRIZA IS NOT NULL AND OPERARIO IS NOT NULL'
) ln
-- JOIN con Nodrizas para obtener el ID por el número de nodriza
LEFT JOIN dbo.Nodrizas n ON ln.NODRIZA = n.NumNodriza
-- JOIN con Operarios para obtener el ID por el nombre del operario
LEFT JOIN dbo.Operarios op ON dbo.LimpiarTexto(ln.OPERARIO) = (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, ''))
WHERE ln.NODRIZA IS NOT NULL
  AND ln.OPERARIO IS NOT NULL
  AND n.Id IS NOT NULL
  AND op.Id IS NOT NULL;

-- =====================================================
-- PASO 3: CREAR ÍNDICES PARA MEJORAR RENDIMIENTO
-- =====================================================

-- Índices en Operarios
CREATE INDEX IX_Operarios_Nombre ON dbo.Operarios(Nombre);
CREATE INDEX IX_Operarios_Apellido1 ON dbo.Operarios(Apellido1) WHERE Apellido1 IS NOT NULL;
CREATE INDEX IX_Operarios_Activo ON dbo.Operarios(Activo);

-- =====================================================
-- PASO 4: CREAR VISTA DE COMPATIBILIDAD
-- =====================================================

-- Vista para mantener compatibilidad con código existente de TablaProductosNodrizas
CREATE VIEW vw_TablaProductosNodrizas_Compatible AS
SELECT
    Id,
    NumNodriza AS IdNodriza,
    IdProducto AS Idproducto,
    CAST(0 AS BIT) AS Obsoleto,
    NULL AS FechaRetirada,
    Activo
FROM dbo.Nodrizas
WHERE Borrada = 0;

-- Vista para mantener compatibilidad con código existente de LotesNodrizas
CREATE VIEW vw_LotesNodrizas_Compatible AS
SELECT
    ln.Id,
    CAST(ln.FechaHora AS DATE) AS FECHA,
    CAST(ln.FechaHora AS TIME) AS HORA,
    n.NumNodriza AS NODRIZA,
    ln.IdProducto AS IDPRODUCTO,
    ln.Lote AS LOTE,
    (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, '')) AS OPERARIO,
    ln.Ubicacion AS UBICACION
FROM dbo.LotesNodrizas ln
LEFT JOIN dbo.Nodrizas n ON ln.NodrizaId = n.Id
LEFT JOIN dbo.Operarios op ON ln.OperarioId = op.Id;

-- =====================================================
-- PASO 5: VERIFICAR RESULTADOS
-- =====================================================

-- Mostrar operarios migrados
SELECT
    'Operarios migrados' as Tabla,
    COUNT(*) as TotalRegistros,
    SUM(CASE WHEN Activo = 1 THEN 1 ELSE 0 END) as Activos,
    SUM(CASE WHEN Activo = 0 THEN 1 ELSE 0 END) as Inactivos
FROM dbo.Operarios;

-- Mostrar nodrizas migradas
SELECT
    'Nodrizas migradas' as Tabla,
    COUNT(*) as TotalRegistros,
    SUM(CASE WHEN Activo = 1 THEN 1 ELSE 0 END) as Activas,
    SUM(CASE WHEN Activo = 0 THEN 1 ELSE 0 END) as Inactivas,
    SUM(CASE WHEN Borrada = 1 THEN 1 ELSE 0 END) as Borradas
FROM dbo.Nodrizas;

-- Mostrar lotes de nodrizas migrados
SELECT
    'LotesNodrizas migrados' as Tabla,
    COUNT(*) as TotalRegistros,
    MIN(FechaHora) as FechaMasAntigua,
    MAX(FechaHora) as FechaMasReciente
FROM dbo.LotesNodrizas;

-- Mostrar algunas nodrizas de ejemplo
SELECT TOP 10
    Id,
    NumNodriza,
    IdProducto,
    Activo,
    Borrada,
    FechaCreacion,
    FechaModificacion
FROM dbo.Nodrizas
ORDER BY NumNodriza;

-- Mostrar algunos lotes de nodrizas de ejemplo
SELECT TOP 10
    ln.Id,
    ln.FechaHora,
    n.NumNodriza,
    ln.IdProducto,
    ln.Lote,
    (op.Nombre + ISNULL(' ' + op.Apellido1, '') + ISNULL(' ' + op.Apellido2, '')) as Operario,
    ln.Ubicacion
FROM dbo.LotesNodrizas ln
LEFT JOIN dbo.Nodrizas n ON ln.NodrizaId = n.Id
LEFT JOIN dbo.Operarios op ON ln.OperarioId = op.Id
ORDER BY ln.FechaHora DESC;

PRINT 'Migración completada exitosamente.';
PRINT 'Tablas creadas: Operarios, Nodrizas, LotesNodrizas';
PRINT 'Mejoras implementadas:';
PRINT '- Operarios con nombre y apellidos separados';
PRINT '- Nodrizas con campo Borrada y auditoría';
PRINT '- LotesNodrizas con FK a Operarios y Nodrizas, fecha unificada';
PRINT 'Vista de compatibilidad creada: vw_TablaProductosNodrizas_Compatible';