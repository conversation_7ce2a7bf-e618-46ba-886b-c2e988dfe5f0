namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using MediatR;
using Repositories.Dato01Lita.Base;

public record GetDetallesLotesQuery(string? IdProducto, bool AgruparPorCodigoyLote)
    : IRequest<ListResult<BarnizDTO>>;

public class
    GetDetallesLotesQueryHandler : IRequestHandler<GetDetallesLotesQuery,
    ListResult<BarnizDTO>>
{
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IIncidenciasRepo _incidenciasRepo;
    private readonly ITablaInspeccionesRepo _tablaInspeccionesRepo;

    public GetDetallesLotesQueryHandler(ITablaInspeccionesRepo tablaInspeccionesRepo,
        IIncidenciasRepo incidenciasRepo, IAArticuRepo aArticuRepo)
    {
        _tablaInspeccionesRepo = tablaInspeccionesRepo;
        _incidenciasRepo = incidenciasRepo;
        _aArticuRepo = aArticuRepo;
    }

    public async Task<ListResult<BarnizDTO>> Handle(
        GetDetallesLotesQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<BarnizDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            // Usar las QueryOptions pasadas o crear unas por defecto
            result.Data = request.SacarViscosidades
                ? await GetBarnicesConViscosidades(request.IdBarniz, request.QueryOptions, cancellationToken)
                : await GetBarnicesSinViscosidades(request.IdBarniz, request.QueryOptions, cancellationToken);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}