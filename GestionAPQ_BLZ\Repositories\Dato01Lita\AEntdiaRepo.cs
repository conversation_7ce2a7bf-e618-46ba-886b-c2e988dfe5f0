﻿namespace GestionAPQ_BLZ.Repositories.Dato01Lita;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AEntdiaRepo : IAEntdiaRepo
{
    private readonly Dato01LitaContext _dbContext;
    private readonly DbSet<AEntdia> _dbSet;

    public AEntdiaRepo(Dato01LitaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<AEntdia>();
    }

    public IQueryable<AEntdia> GetIQueryableEntradas(string? idProducto, QueryOptions options)
    {
        var query = options.SetOptions(_dbSet);
        if (!string.IsNullOrEmpty(idProducto))
            query = query.Where(i => i.Codigo.Equals(idProducto));

        return query;
    }
}