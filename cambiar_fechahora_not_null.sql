-- <PERSON>ript para cambiar la columna FechaHora a NOT NULL después de actualizar los registros
USE ApqLitalsa;

PRINT '=== CAMBIANDO FECHAHORA A NOT NULL ===';

-- 1. Verificar que no hay registros con FechaHora NULL
DECLARE @RegistrosNull INT;
SELECT @RegistrosNull = COUNT(*) FROM dbo.LotesNodrizas WHERE FechaHora IS NULL;

IF @RegistrosNull > 0
BEGIN
    PRINT '❌ ERROR: Aún hay ' + CAST(@RegistrosNull AS NVARCHAR(10)) + ' registros con FechaHora NULL';
    PRINT 'Ejecuta primero el script actualizar_fechas_null.sql';
    RETURN;
END
ELSE
BEGIN
    PRINT '✅ No hay registros con FechaHora NULL. Procediendo...';
END

-- 2. Cambiar la columna a NOT NULL
BEGIN TRY
    ALTER TABLE dbo.LotesNodrizas 
    ALTER COLUMN FechaHora DATETIME2 NOT NULL;
    
    PRINT '✅ Columna FechaHora cambiada a NOT NULL exitosamente';
END TRY
BEGIN CATCH
    PRINT '❌ ERROR al cambiar la columna: ' + ERROR_MESSAGE();
END CATCH

-- 3. Verificar el cambio
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'LotesNodrizas' 
  AND COLUMN_NAME = 'FechaHora';

-- 4. Mostrar estadísticas finales
SELECT 
    'Total de registros en LotesNodrizas' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas

UNION ALL

SELECT 
    'Registros con fecha por defecto (1900-01-01)' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas 
WHERE FechaHora = CAST('1900-01-01 00:00:00' AS DATETIME2)

UNION ALL

SELECT 
    'Registros con fecha por defecto (2024-01-01)' as Descripcion,
    COUNT(*) as Cantidad
FROM dbo.LotesNodrizas 
WHERE FechaHora = CAST('2024-01-01 00:00:00' AS DATETIME2);

PRINT '';
PRINT 'Proceso completado. La columna FechaHora ahora es NOT NULL.';
